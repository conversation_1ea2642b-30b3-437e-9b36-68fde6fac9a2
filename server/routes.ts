import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertContactSubmissionSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes

  // Health check endpoint
  app.get("/api/ping", (req, res) => {
    res.status(200).send("pong");
  });

  // Contact form submission endpoint
  app.post("/api/contact", async (req, res) => {
    try {
      // Validate the request body
      const validatedData = insertContactSubmissionSchema.parse(req.body);

      // Store the contact submission
      const submission = await storage.createContactSubmission(validatedData);

      res.status(201).json({
        message: "Contact submission received",
        submission,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid form data",
          errors: error.format(),
        });
      }

      console.error("Error processing contact submission:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Newsletter signup endpoint
  app.post("/api/newsletter", async (req, res) => {
    try {
      // Validate the email
      const schema = z.object({
        email: z.string().email("Invalid email address"),
      });

      const { email } = schema.parse(req.body);

      // Store the newsletter subscription
      const submission = await storage.createContactSubmission({ email });

      res.status(201).json({
        message: "Newsletter subscription successful",
        submission,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid email address",
          errors: error.format(),
        });
      }

      console.error("Error processing newsletter signup:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
