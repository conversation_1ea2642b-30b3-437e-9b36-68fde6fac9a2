// State type for locations
export type State = "TX" | "FL" | "NY" | "BE";

// Testimonial type
export interface Testimonial {
  id: number;
  name: string;
  firm: string;
  state: State;
  quote: string;
  rating?: number;
  hasVideo?: boolean;
}

// Pricing plan type
export interface PricingPlan {
  name: string;
  subtitle: string;
  price: number;
  features: string[];
  disabledFeatures: string[];
  popular: boolean;
  cta: string;
}

// FAQ type
export interface FAQ {
  question: string;
  answer: string;
}

// Security feature type
export interface SecurityFeature {
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Feature card type
export interface FeatureCard {
  id: number;
  code: string;
  title: string;
  description: string;
  demoTitle: string;
  demoContent: React.ReactNode;
}

// Problem card type
export interface ProblemCard {
  emoji: string;
  title: string;
  description: string;
}

// Logo type
export interface Logo {
  name: string;
  altText: string;
}

// Navigation item type
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

// Form submission type
export interface ContactSubmission {
  email: string;
  name?: string;
  message?: string;
}
