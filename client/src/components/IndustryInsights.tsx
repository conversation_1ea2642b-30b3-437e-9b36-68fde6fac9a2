import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  ArrowUpRight,
} from "lucide-react";

export default function IndustryInsights() {
  return (
    <section className="py-24 bg-[radial-gradient(circle_at_top,_#f8fafc,_#ffffff)] relative overflow-hidden">
      {/* Background Elements with Movement */}
      <div className="absolute inset-0 opacity-50">
        <motion.div
          className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-blue-100 to-transparent rounded-full blur-3xl"
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-tl from-cyan-100 to-transparent rounded-full blur-3xl"
          animate={{
            y: [0, 15, 0],
            x: [0, -8, 0],
            scale: [1, 0.95, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-indigo-50 to-transparent rounded-full blur-3xl"
          animate={{
            rotate: [0, 180, 360],
            scale: [0.8, 1.1, 0.8],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      </div>
      <div className="container-content relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">
              Industry Reality Check
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
            Why Belgian Lawyers Who Embrace AI Are Getting Ahead
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Solo lawyers and small firms in Belgium are embracing AI — not
            to cut corners, but to cut admin overload.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* AI Adoption Card */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
            className="group"
          >
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_20px_60px_rgba(0,0,0,0.12)] transition-all duration-500 hover:-translate-y-2">
              {/* Header */}
              <div className="flex items-center gap-4 mb-8">
                <div className="w-14 h-14 bg-gradient-to-br from-emerald-400 to-green-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <TrendingUp className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    AI Adoption Momentum
                  </h3>
                  <p className="text-sm text-gray-500">
                    Belgium's digital shift in legal practice
                  </p>
                </div>
              </div>

              {/* Modern Data Visualization */}
              <div className="space-y-6 mb-8">
                <div className="relative">
                  <div className="flex items-end justify-between h-32 mb-4">
                    {[
                      { year: "2022", height: "13%", delay: 0.1 },
                      { year: "2023", height: "24%", delay: 0.2 },
                      { year: "2024", height: "41%", delay: 0.3 },
                      { year: "2025", height: "100%", delay: 0.4 },
                    ].map((bar, index) => (
                      <motion.div
                        key={bar.year}
                        initial={{ height: 0 }}
                        whileInView={{ height: bar.height }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: bar.delay }}
                        className="w-12 bg-gradient-to-t from-emerald-400 via-green-400 to-emerald-300 rounded-t-lg shadow-sm"
                      />
                    ))}
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 font-medium">
                    <span>2022</span>
                    <span>2023</span>
                    <span>2024</span>
                    <span className="text-emerald-600 font-bold">2025</span>
                  </div>
                </div>

                {/* Key Stat */}
                <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-100">
                  <div className="flex items-center gap-3 mb-2">
                    <ArrowUpRight className="w-5 h-5 text-emerald-600" />
                    <span className="text-4xl font-bold text-emerald-700">
                      230%
                    </span>
                  </div>
                  <p className="text-sm font-medium text-emerald-700">
                    increase in AI tool adoption among small and solo Belgian practices since 2024
                  </p>
                </div>

                {/* Disclaimer */}
                <p className="text-xs text-gray-400 italic text-center mt-4">
                  Based on EU and Belgian legal tech adoption trends. Source: DESI 2024, CEPEJ, Belgian Bar Associations, AiLex research.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Admin Overload Card */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
            className="group"
          >
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_20px_60px_rgba(0,0,0,0.12)] transition-all duration-500 hover:-translate-y-2">
              {/* Header */}
              <div className="flex items-center gap-4 mb-8">
                <div className="w-14 h-14 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <AlertTriangle className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    The Admin Crisis
                  </h3>
                  <p className="text-sm text-gray-500">
                    Hidden productivity costs in small law practices
                  </p>
                </div>
              </div>

              {/* Modern Progress Indicator */}
              <div className="space-y-6 mb-8">
                <div className="relative">
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-sm font-medium text-gray-700">
                      Admin burden on Belgian small firms
                    </span>
                    <span className="text-2xl font-bold text-orange-600">
                      72%
                    </span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      whileInView={{ width: "72%" }}
                      viewport={{ once: true }}
                      transition={{ duration: 1.2, delay: 0.5 }}
                      className="h-full bg-gradient-to-r from-orange-400 to-red-500 rounded-full shadow-sm"
                    />
                  </div>
                </div>

                {/* Key Issues Grid */}
                <div className="grid grid-cols-3 gap-3">
                  {[
                    {
                      icon: Clock,
                      label: "Time Loss",
                      color: "from-orange-400 to-red-400",
                    },
                    {
                      icon: AlertTriangle,
                      label: "Burnout Risk",
                      color: "from-red-400 to-pink-400",
                    },
                    {
                      icon: Zap,
                      label: "Efficiency Gap",
                      color: "from-pink-400 to-purple-400",
                    },
                  ].map((item, index) => (
                    <motion.div
                      key={item.label}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                      className={`bg-gradient-to-br ${item.color} rounded-xl p-4 text-center text-white`}
                    >
                      <item.icon className="w-5 h-5 mx-auto mb-2" />
                      <div className="text-xs font-medium">{item.label}</div>
                    </motion.div>
                  ))}
                </div>

                {/* Key Stat */}
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100">
                  <div className="flex items-center gap-3 mb-2">
                    <AlertTriangle className="w-5 h-5 text-orange-600" />
                    <span className="text-4xl font-bold text-orange-700">
                      72%
                    </span>
                  </div>
                  <p className="text-sm font-medium text-orange-700">
                    of Belgian solo and small firm lawyers say admin work eats into more than half their billable time
                  </p>
                </div>

                {/* Belgium-specific Disclaimer */}
                <p className="text-xs text-gray-400 italic text-center mt-4">
                  Source: Belgian Bar Association survey (2023), CEPEJ 2024, AiLex internal study
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* General Disclaimer */}
        <p className="text-xs text-gray-400 text-center mt-6">
          Informational only. Figures represent general industry trends and
          external research.
        </p>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 max-w-4xl mx-auto border border-white/30 shadow-[0_8px_32px_rgba(0,0,0,0.06)]">
            <div className="text-lg font-medium text-gray-600 mb-3">
              The competitive gap is widening:
            </div>
            <div className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Firms embracing AiLex are gaining the competitive edge — right
              now.
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
