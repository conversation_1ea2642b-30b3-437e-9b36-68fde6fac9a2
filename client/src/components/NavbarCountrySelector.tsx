import React, { useState, useEffect, useRef } from 'react';
import { Globe, ChevronDown } from 'lucide-react';

interface NavbarCountrySelectorProps {
  isScrolled?: boolean;
}

export default function NavbarCountrySelector({ 
  isScrolled = false 
}: NavbarCountrySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCountryChange = (path: string) => {
    window.location.href = path;
    setIsOpen(false);
  };

  const countries = [
    {
      name: 'Belgium',
      flag: '🇧🇪',
      path: '/be',
      states: []
    },
    {
      name: 'United States',
      flag: '🇺🇸',
      path: '/tx', // Default to Texas
      states: [
        { name: 'Texas', path: '/tx' },
        { name: 'Florida', path: '/fl' },
        { name: 'New York', path: '/ny' }
      ]
    }
  ];

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Globe Icon Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center gap-1 p-2 rounded-lg transition-all duration-200 ease-out ${
          isScrolled 
            ? 'text-gray-300 hover:text-white hover:bg-white/10' 
            : 'text-gray-600 hover:text-[#1EAEDB] hover:bg-gray-50'
        }`}
        aria-label="Select country or region"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Globe className="w-4 h-4" />
        <ChevronDown
          className={`w-3 h-3 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden">
          <div className="py-1">
            {countries.map((country) => (
              <div key={country.name}>
                {/* Country Header */}
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 bg-gray-50 border-b border-gray-100">
                  {country.flag} {country.name}
                </div>
                
                {/* Country/States Options */}
                {country.states.length > 0 ? (
                  // Show states for countries that have them
                  country.states.map((state) => (
                    <button
                      key={state.path}
                      onClick={() => handleCountryChange(state.path)}
                      className="w-full flex items-center gap-3 px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 text-gray-700"
                      role="menuitem"
                    >
                      <span className="flex-1">{state.name}</span>
                    </button>
                  ))
                ) : (
                  // Show country directly for countries without states
                  <button
                    onClick={() => handleCountryChange(country.path)}
                    className="w-full flex items-center gap-3 px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 text-gray-700"
                    role="menuitem"
                  >
                    <span className="flex-1">{country.name}</span>
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
