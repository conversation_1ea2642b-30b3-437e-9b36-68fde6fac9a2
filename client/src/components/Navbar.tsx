import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import NavbarLanguageSelector from "@/components/NavbarLanguageSelector";
import NavbarCountrySelector from "@/components/NavbarCountrySelector";
import { getTranslations, getCurrentLanguage } from "@/utils/translations";
import ailexIcon from "../assets/ailex-icon.png";

type NavbarProps = {
  currentState?: "TX" | "FL" | "NY" | "BE";
  language?: "en" | "fr" | "nl";
};

export default function Navbar({ currentState, language }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [location, setLocation] = useLocation();

  // Get translations for Belgian pages
  const isBelgianPage = currentState === "BE";
  const getCurrentLanguageFromLocation = () => {
    if (location.startsWith('/be-fr')) return 'fr';
    if (location.startsWith('/be-nl')) return 'nl';
    if (location.startsWith('/be')) return 'en';
    return 'en';
  };
  // Use explicit language prop if provided, otherwise detect from location
  const currentLanguage = isBelgianPage ? (language || getCurrentLanguageFromLocation()) : 'en';
  const t = isBelgianPage ? getTranslations(currentLanguage) : null;

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 32);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Function to navigate to homepage or scroll to top
  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Small visual feedback animation
    const target = e.currentTarget;
    target.classList.add("scale-105");
    setTimeout(() => {
      target.classList.remove("scale-105");
    }, 200);

    // Determine the correct homepage based on current language
    const getHomepage = () => {
      if (currentState === "BE") {
        if (location.startsWith('/be-fr')) return '/be-fr';
        if (location.startsWith('/be-nl')) return '/be-nl';
        if (location.startsWith('/be')) return '/be';
      }
      return "/";
    };

    const homepage = getHomepage();

    if (location === homepage) {
      // Already on homepage, just scroll to top
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      // Navigate to homepage
      setLocation(homepage);
    }
  };

  // Function to navigate to homepage and scroll to section
  const navigateToSection = (sectionId: string) => {
    // Determine the correct homepage based on current language
    const getHomepage = () => {
      if (currentState === "BE") {
        if (location.startsWith('/be-fr')) return '/be-fr';
        if (location.startsWith('/be-nl')) return '/be-nl';
        if (location.startsWith('/be')) return '/be';
      }
      return "/";
    };

    const homepage = getHomepage();

    if (location === homepage) {
      // Already on homepage, just scroll
      document.getElementById(sectionId)?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    } else {
      // Navigate to homepage first, then scroll
      setLocation(homepage);
      setTimeout(() => {
        document.getElementById(sectionId)?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  };

  return (
    <header
      className={`fixed w-full z-50 top-0 h-16 flex items-center transition-default ${
        isScrolled ? "bg-navy shadow-md" : ""
      }`}
    >
      <div className="container-full w-full flex items-center justify-between">
        <a
          href="#"
          onClick={handleLogoClick}
          className="flex items-center cursor-pointer transition-all duration-200 ease-out"
        >
          <motion.img
            src={ailexIcon}
            alt="AiLex Icon"
            className="h-8 w-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
          <span
            className={`ml-3 text-lg font-bold tracking-wide ${isScrolled ? "text-white" : "text-navy"}`}
          >
            AiLex
          </span>
          {currentState && currentState !== "BE" && (
            <span className="ml-2 code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy">
              {currentState}
            </span>
          )}
        </a>

        {/* Language Selector for Belgian pages */}
        <NavbarLanguageSelector currentState={currentState} isScrolled={isScrolled} />

        <div className="hidden md:flex items-center space-x-8">
          <button
            onClick={() => navigateToSection("features")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            {t ? t.navFeatures : "Features"}
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <button
            onClick={() => navigateToSection("pricing")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            {t ? t.navPricing : "Pricing"}
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <Link
            href={(() => {
              if (currentState === "BE") {
                if (location.startsWith('/be-fr')) return '/be-fr/blog';
                if (location.startsWith('/be-nl')) return '/be-nl/blog';
                if (location.startsWith('/be')) return '/be/blog';
              }
              return "/blog";
            })()}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            {t ? t.navBlog : "Blog"}
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </Link>
          <button
            onClick={() => navigateToSection("faq")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            {t ? t.navFaq : "FAQ"}
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigateToSection("contact")}
              className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
            >
              {t ? t.navContact : "Contact"}
              <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
            </button>
            <NavbarCountrySelector isScrolled={isScrolled} />
          </div>
        </div>

        <div className="flex items-center space-x-3">


          <a
            href="/login"
            className="text-sm px-6 py-3 border border-[#1EAEDB] text-[#1EAEDB] rounded-xl hover:bg-[#1EAEDB] hover:text-white transition-all duration-300 ease-out font-medium"
          >
            {t ? t.navLogin : "Login"}
          </a>
          <a href="/login" className="btn-primary text-sm">
            {t ? t.navStartTrial : "Start free trial"}
          </a>

          <Sheet>
            <SheetTrigger asChild>
              <button className="ml-4 md:hidden text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-6 w-6 ${isScrolled ? "text-white" : ""}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px]">
              <div className="flex flex-col space-y-4 mt-8">
                <a
                  href="#"
                  onClick={handleLogoClick}
                  className="text-lg font-medium flex items-center transition-all duration-200 ease-out hover:text-primary"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-primary"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Top
                </a>
                <button
                  onClick={() => navigateToSection("features")}
                  className="text-lg font-medium text-left"
                >
                  {t ? t.navFeatures : "Features"}
                </button>
                <button
                  onClick={() => navigateToSection("pricing")}
                  className="text-lg font-medium text-left"
                >
                  {t ? t.navPricing : "Pricing"}
                </button>
                <Link href={(() => {
                  if (currentState === "BE") {
                    if (location.startsWith('/be-fr')) return '/be-fr/blog';
                    if (location.startsWith('/be-nl')) return '/be-nl/blog';
                    if (location.startsWith('/be')) return '/be/blog';
                  }
                  return "/blog";
                })()} className="text-lg font-medium">
                  {t ? t.navBlog : "Resources"}
                </Link>
                <a href="/login" className="text-lg font-medium">
                  {t ? t.navLogin : "Login"}
                </a>
                <a href="/login" className="btn-primary mt-4 text-center">
                  {t ? t.navStartTrial : "Start free trial"}
                </a>



                {/* State selector in mobile menu */}
                {currentState && (
                  <div className="mt-6">
                    <p className="text-sm font-medium mb-2">Switch State:</p>
                    <div className="flex flex-wrap gap-2">
                      <Link
                        href="/tx"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "TX" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        Texas
                      </Link>
                      <Link
                        href="/fl"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "FL" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        Florida
                      </Link>
                      <Link
                        href="/ny"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "NY" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        New York
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
