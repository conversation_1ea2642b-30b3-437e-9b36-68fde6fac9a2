import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { ChevronDown } from 'lucide-react';
import { getLanguageFromPath, setLanguageCookie } from '@/utils/languageDetection';

interface NavbarLanguageSelectorProps {
  currentState?: "TX" | "FL" | "NY" | "BE";
  isScrolled?: boolean;
}

export default function NavbarLanguageSelector({ 
  currentState, 
  isScrolled = false 
}: NavbarLanguageSelectorProps) {
  const [location] = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Only show for Belgian pages
  if (currentState !== "BE") {
    return null;
  }

  // Get current selection based on the current route
  const getCurrentSelection = () => {
    if (location.startsWith('/be-fr')) return 'BE-FR';
    if (location.startsWith('/be-nl')) return 'BE-NL';
    if (location.startsWith('/be')) return 'BE-EN';
    return 'BE-EN'; // Default fallback
  };

  const getDisplayText = () => {
    const selection = getCurrentSelection();
    switch (selection) {
      case 'BE-FR': return 'BE-FR';
      case 'BE-NL': return 'BE-NL';
      case 'BE-EN':
      default: return 'BE-EN';
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelectionChange = (value: string) => {
    // Handle Belgian language variants
    if (value === 'BE-EN') {
      setLanguageCookie('en');
      window.location.href = '/be';
    } else if (value === 'BE-FR') {
      setLanguageCookie('fr');
      window.location.href = '/be-fr';
    } else if (value === 'BE-NL') {
      setLanguageCookie('nl');
      window.location.href = '/be-nl';
    } else {
      // Handle other states
      window.location.href = `/${value.toLowerCase()}`;
    }
    setIsOpen(false);
  };

  const options = [
    { value: 'BE-EN', label: 'BE-EN (English)', flag: '' },
    { value: 'BE-FR', label: 'BE-FR (Français)', flag: '' },
    { value: 'BE-NL', label: 'BE-NL (Nederlands)', flag: '' },
  ];

  const currentSelection = getCurrentSelection();

  return (
    <div className="relative ml-3" ref={dropdownRef}>
      {/* Trigger Button - Pill Style Badge */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ease-out bg-[#B8FF5C] text-navy hover:bg-[#A8EF4C]"
        aria-label="Select country and language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className="code">{getDisplayText()}</span>
        <ChevronDown
          className={`w-3 h-3 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute left-0 top-full mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden md:left-0 sm:left-auto sm:right-0">
          <div className="py-1">
            {/* Belgian Options */}
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 bg-gray-50 border-b border-gray-100">
              🇧🇪 Belgium
            </div>
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelectionChange(option.value)}
                className={`w-full flex items-center gap-3 px-4 py-3 text-sm text-left hover:bg-gray-50 transition-colors duration-150 ${
                  currentSelection === option.value
                    ? 'bg-blue-50 text-blue-700 font-medium'
                    : 'text-gray-700'
                }`}
                role="menuitem"
              >
                {option.flag && <span className="text-base">{option.flag}</span>}
                <span className="flex-1">{option.label}</span>
                {currentSelection === option.value && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
