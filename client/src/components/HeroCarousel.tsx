import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

type HeroCarouselProps = {
  state?: "TX" | "FL" | "NY" | "BE";
};

const carouselItems = [
  {
    id: 1,
    title: "Research Module",
    content: (
      <div className="h-full flex flex-col">
        <div className="code text-xs text-gray-500 mb-2">Research Module</div>
        <div className="bg-[#F5F7FA] p-4 rounded-lg mb-4">
          <div className="flex items-start gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-primary"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                clipRule="evenodd"
              />
            </svg>
            <div>
              <div className="font-medium">Bailey v. State.pdf</div>
              <div className="text-xs text-gray-500">Analyzing document...</div>
            </div>
          </div>
        </div>

        <div className="border rounded-lg p-4 flex-grow">
          <div className="text-sm mb-3 font-medium">Citations Found:</div>
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <div className="bg-primary bg-opacity-10 rounded p-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-primary"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium">
                  Texas Penal Code § 49.04(b)
                </div>
                <div className="text-xs text-gray-500">
                  DWI, Second Offense (Class A)
                </div>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <div className="bg-primary bg-opacity-10 rounded p-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-primary"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium">
                  Ex parte Gutierrez, 987 S.W.2d 227
                </div>
                <div className="text-xs text-gray-500">
                  Tex. Crim. App. 1999
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: 2,
    title: "Voice Intake Agent",
    content: (
      <div className="h-full flex flex-col">
        <div className="code text-xs text-gray-500 mb-2">
          Voice Intake Agent
        </div>
        <div className="flex items-center gap-2 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-red-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
              clipRule="evenodd"
            />
          </svg>
          <div className="font-medium">Call Recording - 05/12/2023</div>
        </div>

        <div className="relative h-16 mb-4">
          <svg
            className="w-full h-full"
            viewBox="0 0 400 80"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0,40 Q10,10 20,40 T40,40 T60,40 T80,40 T100,40 T120,40 T140,40 T160,40 T180,40 T200,40 T220,40 T240,40 T260,40 T280,40 T300,40 T320,40 T340,40 T360,40 T380,40 T400,40"
              fill="none"
              stroke="#1EAEDB"
              strokeWidth="2"
            />
          </svg>
        </div>

        <div className="border rounded-lg p-4 flex-grow">
          <div className="text-sm mb-3 font-medium">Transcript:</div>
          <div className="space-y-3">
            <div className="flex gap-2">
              <div className="shrink-0 w-8 h-8 bg-navy rounded-full flex items-center justify-center text-white text-xs">
                AI
              </div>
              <div className="grow">
                <div className="text-xs text-gray-500">AiLex Agent:</div>
                <div className="text-sm">
                  Hello, this is AiLex intake for Smith Law. How can I help you
                  today?
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <div className="shrink-0 w-8 h-8 bg-[#F5F7FA] rounded-full flex items-center justify-center text-navy text-xs">
                CL
              </div>
              <div className="grow">
                <div className="text-xs text-gray-500">Client:</div>
                <div className="text-sm">
                  Hi, I need help with a custody modification case...
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: 3,
    title: "Case Management",
    content: (
      <div className="h-full flex flex-col">
        <div className="code text-xs text-gray-500 mb-2">Case Management</div>

        <div className="flex justify-between mb-4">
          <div className="font-medium">Active Matters</div>
          <div className="code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy">
            14 total
          </div>
        </div>

        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="bg-[#F5F7FA] rounded p-2 text-center text-sm">
            <div className="text-xs text-gray-500">New</div>
            <div className="font-medium">5</div>
          </div>
          <div className="bg-[#F5F7FA] rounded p-2 text-center text-sm">
            <div className="text-xs text-gray-500">In Progress</div>
            <div className="font-medium">7</div>
          </div>
          <div className="bg-[#F5F7FA] rounded p-2 text-center text-sm">
            <div className="text-xs text-gray-500">Ready</div>
            <div className="font-medium">2</div>
          </div>
        </div>

        <div className="border rounded-lg divide-y flex-grow">
          <div className="p-3 hover:bg-[#F5F7FA] transition-default">
            <div className="flex justify-between">
              <div className="font-medium">Johnson v. ABC Corp</div>
              <div className="code text-xs bg-primary bg-opacity-10 rounded-full px-2 py-0.5 text-primary">
                Employment
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Draft response due 05/15
            </div>
          </div>

          <div className="p-3 hover:bg-[#F5F7FA] transition-default">
            <div className="flex justify-between">
              <div className="font-medium">Rodriguez Estate</div>
              <div className="code text-xs bg-primary bg-opacity-10 rounded-full px-2 py-0.5 text-primary">
                Probate
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Hearing scheduled 05/20
            </div>
          </div>
        </div>
      </div>
    ),
  },
];

export default function HeroCarousel({ state }: HeroCarouselProps) {
  const [activeSlide, setActiveSlide] = useState(1);

  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveSlide((prev) => (prev % 3) + 1);
    }, 6000);

    return () => clearInterval(interval);
  }, []);

  // Get carousel item
  const getCarouselItem = (id: number) => {
    return carouselItems.find((item) => item.id === id)?.content;
  };

  return (
    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden h-[400px]">
      <AnimatePresence mode="wait">
        <motion.div
          key={activeSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="absolute inset-0 p-6 pb-14" // Increased bottom padding for navigation dots
        >
          {getCarouselItem(activeSlide)}
        </motion.div>
      </AnimatePresence>

      {/* State pill */}
      {state && (
        <div className="absolute top-6 right-6 code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy z-20">
          {state}-Ready
        </div>
      )}

      {/* Navigation dots - positioned at the bottom with clear spacing */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-3 z-20">
        {[1, 2, 3].map((id) => (
          <button
            key={id}
            onClick={() => setActiveSlide(id)}
            className={`w-3 h-3 rounded-full transition-colors duration-200 ${
              activeSlide === id ? "bg-primary" : "bg-gray-300"
            }`}
            aria-label={`Slide ${id}`}
          />
        ))}
      </div>
    </div>
  );
}
