import { useState, useRef } from "react";
import { motion } from "framer-motion";
import { getCurrentLanguage, getTranslations } from "@/utils/translations";

export default function SocialProof() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isPaused, setIsPaused] = useState(false);

  // Get translations based on current language
  const currentLanguage = getCurrentLanguage();
  const t = getTranslations(currentLanguage);

  // Define the practice areas we want to display
  const practiceAreas = [
    { code: "PI", name: t.practiceAreas.personalInjury },
    { code: "FAM", name: t.practiceAreas.family },
    { code: "CR", name: t.practiceAreas.criminal },
    { code: "CIV", name: t.practiceAreas.civilLitigation },
    { code: "EMP", name: t.practiceAreas.employment },
    { code: "IMM", name: t.practiceAreas.immigration },
    { code: "CORP", name: t.practiceAreas.businessCorporate },
    { code: "IP", name: t.practiceAreas.intellectualProperty },
  ];

  // Duplicate the array to create a seamless looping effect
  const duplicatedAreas = [...practiceAreas, ...practiceAreas];

  return (
    <section className="py-6 border-t border-b border-gray-200 overflow-hidden relative">
      {/* Fade effect on the left */}
      <div
        className="absolute left-0 top-0 h-full w-24 z-10 pointer-events-none"
        style={{
          background: "linear-gradient(90deg, white, rgba(255,255,255,0))",
        }}
      />

      {/* Fade effect on the right */}
      <div
        className="absolute right-0 top-0 h-full w-24 z-10 pointer-events-none"
        style={{
          background: "linear-gradient(270deg, white, rgba(255,255,255,0))",
        }}
      />

      <div className="relative overflow-hidden">
        <div
          ref={scrollRef}
          className="flex py-3 animate-marquee"
          style={{
            animationPlayState: isPaused ? "paused" : "running",
          }}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          {duplicatedAreas.map((area, index) => (
            <div key={`${area.code}-${index}`} className="flex-shrink-0 mx-4">
              <motion.div
                className="bg-white rounded-lg shadow-sm px-3 py-2 border border-gray-100 flex items-center hover:shadow-md transition-all group"
                whileHover={{ y: -3, scale: 1.03 }}
                transition={{ duration: 0.2 }}
                title={area.name}
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="practice-pill w-10 h-6 overflow-hidden rounded-sm shadow-sm bg-gray-50 flex items-center justify-center relative
                          group-hover:bg-primary group-hover:text-white transition-all duration-200"
                  >
                    {/* Practice area abbreviation that changes color on hover */}
                    <div className="text-xs font-medium transition-colors duration-200">
                      {area.code}
                    </div>
                  </div>
                  <span className="text-sm font-medium text-gray-600">
                    {area.name}
                  </span>
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
