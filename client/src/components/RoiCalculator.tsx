import { useState, useEffect, useRef } from "react";
import { motion, useAnimation } from "framer-motion";
import { useTranslations } from "../utils/translations";

export default function RoiCalculator() {
  const t = useTranslations();

  // Default values for each role
  const [attorneyHours, setAttorneyHours] = useState(4);
  const [paralegalHours, setParalegalHours] = useState(6);
  const [assistantHours, setAssistantHours] = useState(3);

  const [totalHoursSaved, setTotalHoursSaved] = useState(0);
  const [totalMoneySaved, setTotalMoneySaved] = useState(0);
  const [isInView, setIsInView] = useState(false);

  const barControls = useAnimation();

  // Refs for showing tooltips
  const attorneyTooltipRef = useRef(null);
  const paralegalTooltipRef = useRef(null);
  const assistantTooltipRef = useRef(null);

  // Hourly rates for each role
  const ATTORNEY_RATE = 150;
  const PARALEGAL_RATE = 50;
  const ASSISTANT_RATE = 35;

  // Monthly calculation variables
  const attorneyMonthlyHours = attorneyHours * 4;
  const paralegalMonthlyHours = paralegalHours * 4;
  const assistantMonthlyHours = assistantHours * 4;

  const attorneySavings = attorneyMonthlyHours * ATTORNEY_RATE;
  const paralegalSavings = paralegalMonthlyHours * PARALEGAL_RATE;
  const assistantSavings = assistantMonthlyHours * ASSISTANT_RATE;

  // Update calculations when any hours value changes
  useEffect(() => {
    // Calculate monthly hours (4 weeks)
    const totalMonthlyHours =
      attorneyMonthlyHours + paralegalMonthlyHours + assistantMonthlyHours;

    // Calculate monthly savings by role
    const totalSavings = attorneySavings + paralegalSavings + assistantSavings;

    // Update state
    setTotalHoursSaved(totalMonthlyHours);
    setTotalMoneySaved(totalSavings);

    // Animate the bar height (max theoretical monthly hours = 40hrs × 3 roles × 4 weeks = 480)
    const MAX_HOURS = 480;
    const barPercentage = Math.min((totalMonthlyHours / MAX_HOURS) * 100, 100);

    barControls.start({
      height: `${barPercentage}%`,
      transition: { duration: 0.5 },
    });
  }, [attorneyHours, paralegalHours, assistantHours, barControls]);

  // Tooltip toggle functions
  const toggleTooltip = (ref: React.RefObject<HTMLDivElement>) => {
    if (ref.current) {
      ref.current.classList.toggle("hidden");
    }
  };

  // Pulse animation for savings numbers
  const pulseVariants = {
    pulse: {
      scale: [1, 1.05, 1],
      opacity: [1, 0.9, 1],
      transition: { duration: 0.8, ease: "easeInOut" },
    },
  };

  // Reusable slider component with premium Framer-style styling
  const RoleSlider = ({
    title,
    subtitle,
    value,
    onChange,
    color = "bg-primary",
    tooltipRef,
    hourlyRate,
    icon,
  }: {
    title: string;
    subtitle: string;
    value: number;
    onChange: (value: number) => void;
    color?: string;
    tooltipRef: React.RefObject<HTMLDivElement>;
    hourlyRate: number;
    icon?: string | React.ReactNode;
  }) => {
    // Calculate monthly savings based on slider value
    const calculateMonthlySavings = () => {
      const monthlySavings = Math.round(value * hourlyRate * 4);
      return monthlySavings;
    };

    const getDynamicHintText = () => {
      const monthlySavings = calculateMonthlySavings();
      return `${value} hrs/week saves €${monthlySavings.toLocaleString()}/month`;
    };

    // Get gradient colors based on role - exactly matching Industry Insights palette
    const getGradientColors = () => {
      return color === "bg-blue-600"
        ? ["#60a5fa", "#3b82f6"] // Attorney: blue-400 to blue-500 (softer, lighter blue)
        : color === "bg-teal-600"
          ? ["#fb923c", "#ef4444"] // Paralegal: orange-400 to red-500 (matching Admin Crisis card)
          : ["#f472b6", "#a855f7"]; // Assistant: pink-400 to purple-500 (matching Efficiency Gap)
    };

    const [color1, color2] = getGradientColors();

    return (
      <motion.div
        className="mb-8 p-7 rounded-2xl bg-white/60 backdrop-blur-md border border-white/30 shadow-[0_4px_16px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_32px_rgba(0,0,0,0.08)] transition-all duration-300 hover:border-white/40 relative overflow-hidden"
        whileHover={{ scale: 1.02, y: -4 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        {/* Decorative colored circle in the background */}
        <div
          className={`absolute top-0 right-0 w-24 h-24 rounded-full opacity-10 transform translate-x-8 -translate-y-8`}
          style={{
            background: `linear-gradient(135deg, ${color1}, ${color2})`,
          }}
        ></div>

        {/* Decorative icon watermark */}
        <div className="absolute bottom-6 right-6 opacity-5">
          {typeof icon === "string" ? (
            <span className="text-4xl">{icon}</span>
          ) : (
            <div className="w-12 h-12 text-gray-300">{icon}</div>
          )}
        </div>

        <div className="flex justify-between items-start mb-4 relative z-10">
          <div className="flex items-start space-x-3">
            {icon && (
              <div className="flex items-center">
                {typeof icon === "string" ? (
                  <span className="text-xl">{icon}</span>
                ) : (
                  icon
                )}
              </div>
            )}
            <div>
              <h4 className="font-semibold text-gray-800 text-lg tracking-tight">
                {title}
              </h4>
              <p className="text-gray-500 text-sm">{subtitle}</p>
            </div>
          </div>

          <div className="relative">
            <div className="text-gray-500 px-2 py-1 bg-gray-50 rounded-full">
              <span className="font-normal">€{hourlyRate}/hr</span>
            </div>

            <div
              ref={tooltipRef}
              className="hidden absolute right-0 top-10 bg-gray-800 text-white text-xs rounded-lg py-2 px-3 w-48 z-10 shadow-lg"
            >
              Based on national average rates for this role
              <div className="absolute -top-2 right-4 w-3 h-3 bg-gray-800 transform rotate-45"></div>
            </div>
          </div>
        </div>

        <div className="space-y-4 mt-6">
          <style
            dangerouslySetInnerHTML={{
              __html: `
            /* Base slider styling */
            input[type="range"] {
              -webkit-appearance: none;
              height: 8px;
              border-radius: 20px;
              background: #e5e7eb;
              outline: none;
            }
            
            /* Role-specific thumb colors */
            input[type="range"].attorney-slider::-webkit-slider-thumb {
              -webkit-appearance: none;
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: linear-gradient(135deg, #60a5fa, #3b82f6);
              cursor: pointer;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
            
            input[type="range"].paralegal-slider::-webkit-slider-thumb {
              -webkit-appearance: none;
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: linear-gradient(135deg, #fb923c, #ef4444);
              cursor: pointer;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
            
            input[type="range"].assistant-slider::-webkit-slider-thumb {
              -webkit-appearance: none;
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: linear-gradient(135deg, #f472b6, #a855f7);
              cursor: pointer;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
            
            /* Hover effects */
            input[type="range"].attorney-slider::-webkit-slider-thumb:hover {
              box-shadow: 0 3px 8px rgba(96, 165, 250, 0.5);
              transform: scale(1.1);
            }
            
            input[type="range"].paralegal-slider::-webkit-slider-thumb:hover {
              box-shadow: 0 3px 8px rgba(251, 146, 60, 0.5);
              transform: scale(1.1);
            }
            
            input[type="range"].assistant-slider::-webkit-slider-thumb:hover {
              box-shadow: 0 3px 8px rgba(244, 114, 182, 0.5);
              transform: scale(1.1);
            }
            
            /* Firefox support */
            input[type="range"].attorney-slider::-moz-range-thumb {
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: #2563eb;
              cursor: pointer;
              border: none;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
            
            input[type="range"].paralegal-slider::-moz-range-thumb {
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: #0D9488;
              cursor: pointer;
              border: none;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
            
            input[type="range"].assistant-slider::-moz-range-thumb {
              width: 22px;
              height: 22px;
              border-radius: 50%;
              background: #7c3aed;
              cursor: pointer;
              border: none;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
              transition: all 0.15s ease;
            }
          `,
            }}
          />

          <input
            type="range"
            min="0"
            max="40"
            step="1"
            value={value}
            onChange={(e) => onChange(parseInt(e.target.value))}
            className={`w-full appearance-none cursor-pointer ${
              hourlyRate === 150
                ? "attorney-slider"
                : hourlyRate === 50
                  ? "paralegal-slider"
                  : "assistant-slider"
            }`}
          />

          <div className="flex justify-between items-center text-sm text-gray-500">
            <span className="text-xs uppercase tracking-wider">0 hrs</span>
            <div
              className={`font-normal px-3 py-1 rounded-full text-white text-sm min-w-[140px] text-center`}
              style={{
                background: `linear-gradient(135deg, ${color1}, ${color2})`,
              }}
            >
              {value} hrs/week saved
            </div>
            <span className="text-xs uppercase tracking-wider">40 hrs</span>
          </div>

          {/* Dynamic savings text */}
          <p
            className="text-sm mt-2"
            style={{
              color:
                hourlyRate === 150
                  ? "#3b82f6"
                  : hourlyRate === 50
                    ? "#ef4444"
                    : "#a855f7",
            }}
          >
            {value} hrs/week saves{" "}
            <span className="font-bold">
              €{Math.round(value * hourlyRate * 4).toLocaleString()}
            </span>
            /month
          </p>
        </div>
      </motion.div>
    );
  };

  return (
    <section className="py-24 bg-[radial-gradient(ellipse_at_top,_#f0f9ff_0%,_#e0f2fe_25%,_#f8fafc_50%,_#ffffff_70%),_radial-gradient(ellipse_at_bottom,_#f0f9ff_0%,_#e0f2fe_25%,_#f8fafc_50%,_#ffffff_70%)] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-50">
        <div className="absolute top-20 right-10 w-72 h-72 bg-gradient-to-br from-emerald-100 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-gradient-to-tl from-blue-100 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container-content relative z-10 max-w-[1200px] mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          onViewportEnter={() => setIsInView(true)}
          viewport={{ once: true }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-16 text-center"
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">
              {t.roiCalculator.badge}
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-center bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
            {t.roiCalculator.mainTitle}
          </h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-4xl mx-auto leading-relaxed">
            {t.roiCalculator.mainSubtitle}
          </p>
        </motion.div>

        <motion.div
          className="bg-white/70 backdrop-blur-xl rounded-3xl p-8 md:p-10 mx-auto overflow-hidden border border-white/20 shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_20px_60px_rgba(0,0,0,0.12)] transition-all duration-500 hover:-translate-y-2"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
        >
          <div className="relative z-10">
            <h3 className="text-2xl font-semibold mb-8 text-gray-800 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 mr-2 text-blue-600"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 1v22M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
              {t.roiCalculator.estimateYourSavings}
            </h3>

            {/* Two-column layout with sliders on left, results on right */}
            <div className="grid lg:grid-cols-2 gap-10">
              {/* Left column - Sliders */}
              <div className="space-y-8">
                <p className="text-sm text-gray-500 mb-4">
                  {t.roiCalculator.useSlidersInstruction}
                </p>
                <RoleSlider
                  title={t.roiCalculator.lawyerTime}
                  subtitle={t.roiCalculator.lawyerTimeSubtitle}
                  value={attorneyHours}
                  onChange={setAttorneyHours}
                  color="bg-blue-600"
                  tooltipRef={attorneyTooltipRef}
                  hourlyRate={ATTORNEY_RATE}
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-5 h-5 text-gray-400 stroke-[1.5]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 6V4M12 8v12M8 18h8m-4-4h4M9 14H4M20 14h-5M15 10h5M4 10h5"
                      />
                    </svg>
                  }
                />

                <RoleSlider
                  title={t.roiCalculator.juristTime}
                  subtitle={t.roiCalculator.juristTimeSubtitle}
                  value={paralegalHours}
                  onChange={setParalegalHours}
                  color="bg-teal-600"
                  tooltipRef={paralegalTooltipRef}
                  hourlyRate={PARALEGAL_RATE}
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-5 h-5 text-gray-400 stroke-[1.5]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  }
                />

                <RoleSlider
                  title={t.roiCalculator.legalAdminTime}
                  subtitle={t.roiCalculator.legalAdminTimeSubtitle}
                  value={assistantHours}
                  onChange={setAssistantHours}
                  color="bg-purple-600"
                  tooltipRef={assistantTooltipRef}
                  hourlyRate={ASSISTANT_RATE}
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-5 h-5 text-gray-400 stroke-[1.5]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  }
                />
              </div>

              {/* Right column - Results */}
              <motion.div
                className="flex items-center w-full"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                key={`${attorneyHours}-${paralegalHours}-${assistantHours}`} // Re-animate on any slider change
              >
                <div className="bg-white/60 backdrop-blur-md rounded-3xl border border-white/30 shadow-[0_4px_16px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_32px_rgba(0,0,0,0.08)] transition-all duration-300 p-6 sm:p-8 w-full relative overflow-hidden">
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-48 h-48 bg-gradient-to-b from-emerald-100/30 to-transparent rounded-full transform translate-x-16 -translate-y-16 opacity-70"></div>
                  <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-t from-blue-100/30 to-transparent rounded-full transform -translate-x-16 translate-y-16 opacity-70"></div>

                  <div className="relative z-10">
                    {/* Section title */}
                    <div className="mb-5">
                      <h4 className="text-base sm:text-lg font-semibold text-gray-700">
                        {t.roiCalculator.monthlySavingsSummary}
                      </h4>
                    </div>

                    {/* Two-column cards for time and value - always show horizontally */}
                    <div className="grid grid-cols-2 gap-3 sm:gap-5 mb-5">
                      {/* Monthly Time Savings */}
                      <div className="flex flex-col bg-gradient-to-br from-white to-blue-50/40 backdrop-blur-sm rounded-2xl p-3 sm:p-5 shadow-sm border border-blue-200/50 transition-all duration-300 hover:shadow-md overflow-hidden">
                        <div className="mb-2 border-l-4 border-blue-500 pl-2">
                          <span className="text-gray-700 text-xs sm:text-sm uppercase tracking-wider font-medium">
                            {t.roiCalculator.monthlyTimeSavings}
                          </span>
                        </div>

                        <div className="flex flex-col mt-3">
                          <div className="flex flex-wrap items-baseline gap-3">
                            <motion.div
                              className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight text-blue-600"
                              style={{
                                lineHeight: "1.1",
                                maxWidth: "100%",
                              }}
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.5, type: "spring" }}
                              key={`hours-${totalHoursSaved}`}
                              variants={pulseVariants}
                              whileInView="pulse"
                            >
                              {totalHoursSaved}
                            </motion.div>
                            <div className="text-gray-600 text-sm sm:text-lg md:text-xl font-medium whitespace-nowrap">
                              hours
                            </div>
                          </div>

                          {/* Extra context */}
                          <div className="mt-3 text-sm text-blue-600 font-medium">
                            That's {Math.round(totalHoursSaved / 4)} hours every
                            {t.roiCalculator.hoursEveryWeek}
                          </div>
                        </div>
                      </div>

                      {/* Estimated Value - Enhanced styling for the emotional anchor point */}
                      <div className="flex flex-col bg-gradient-to-br from-blue-50 to-indigo-50/60 backdrop-blur-sm rounded-2xl p-3 sm:p-5 shadow-md border border-blue-300/50 transition-all duration-300 hover:shadow-lg relative overflow-hidden">
                        {/* Corner accent */}
                        <div className="absolute top-0 right-0 w-16 h-16 overflow-hidden">
                          <div className="absolute transform rotate-45 bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-bold py-1 right-[-35px] top-[12px] w-[120px] text-center">
                            SAVINGS
                          </div>
                        </div>

                        <div className="mb-4 border-l-4 border-blue-500 pl-2">
                          <span className="text-gray-700 text-sm sm:text-base uppercase tracking-wider font-normal">
                            {t.roiCalculator.estimatedValue}
                          </span>
                        </div>

                        <div className="flex flex-col mt-3">
                          <div className="flex flex-wrap items-baseline gap-3">
                            <motion.div
                              className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight"
                              style={{
                                background:
                                  "linear-gradient(135deg, #3b82f6, #1d4ed8)",
                                WebkitBackgroundClip: "text",
                                WebkitTextFillColor: "transparent",
                                textShadow: "0 1px 2px rgba(0,0,0,0.1)",
                                maxWidth: "100%",
                                lineHeight: "1.1",
                              }}
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{
                                duration: 0.5,
                                type: "spring",
                                delay: 0.1,
                              }}
                              key={`money-${totalMoneySaved}`}
                              variants={pulseVariants}
                              whileInView="pulse"
                            >
                              €{totalMoneySaved.toLocaleString()}
                            </motion.div>
                            <div className="text-gray-600 text-sm sm:text-lg md:text-xl font-medium whitespace-nowrap">
                              {t.roiCalculator.perMonth}
                            </div>
                          </div>

                          {/* Extra emphasis */}
                          <div
                            className="mt-2 text-xs font-medium"
                            style={{
                              background:
                                "linear-gradient(135deg, #2563eb, #1d4ed8)",
                              WebkitBackgroundClip: "text",
                              WebkitTextFillColor: "transparent",
                              textShadow: "0 1px 2px rgba(0,0,0,0.1)",
                            }}
                          >
                            That's €{(totalMoneySaved * 12).toLocaleString()} in
                            annual savings
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Mobile progress bar */}
                    <div className="lg:hidden h-3 w-full relative my-6 bg-gray-100 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-primary to-blue-500 rounded-full"
                        style={{
                          width: `${Math.min((totalHoursSaved / 480) * 100, 100)}%`,
                        }}
                        transition={{ duration: 0.5 }}
                      ></motion.div>
                    </div>

                    {/* Role breakdown with vertical bar */}
                    <div className="mt-4 space-y-0">
                      <h4 className="text-xs uppercase tracking-wider text-gray-500 mb-2">
                        {t.roiCalculator.roleByRoleBreakdown}
                      </h4>

                      <div
                        className="border-l-4 pl-3 mb-3 transition-all duration-300 hover:translate-x-1"
                        style={{ borderColor: "#60a5fa" }}
                      >
                        <div className="p-3 sm:p-4 bg-gradient-to-br from-white to-blue-50/30 backdrop-blur-sm rounded-lg border border-blue-100 shadow-sm">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="w-5 h-5 text-gray-400 stroke-[1.5] mr-2"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M12 6V4M12 8v12M8 18h8m-4-4h4M9 14H4M20 14h-5M15 10h5M4 10h5"
                                />
                              </svg>
                              <span
                                className="font-medium text-sm sm:text-base"
                                style={{ color: "#3b82f6" }}
                              >
                                {t.roiCalculator.lawyer}
                              </span>
                            </div>
                            <motion.span
                              className="text-xl sm:text-2xl font-bold"
                              style={{ color: "#3b82f6" }}
                              key={`attorney-${attorneySavings}`}
                              variants={pulseVariants}
                              whileInView="pulse"
                            >
                              €{attorneySavings.toLocaleString()}
                            </motion.span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1 pl-7">
                            {attorneyMonthlyHours} hours × €{ATTORNEY_RATE}/hr
                          </div>
                        </div>
                      </div>

                      <div
                        className="border-l-4 pl-3 mb-3 transition-all duration-300 hover:translate-x-1"
                        style={{ borderColor: "#fb923c" }}
                      >
                        <div className="p-3 sm:p-4 bg-gradient-to-br from-white to-orange-50/30 backdrop-blur-sm rounded-lg border border-orange-100 shadow-sm">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="w-5 h-5 text-gray-400 stroke-[1.5] mr-2"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                              <span
                                className="font-medium text-sm sm:text-base"
                                style={{ color: "#ef4444" }}
                              >
                                {t.roiCalculator.jurist}
                              </span>
                            </div>
                            <motion.span
                              className="text-xl sm:text-2xl font-bold"
                              style={{ color: "#ef4444" }}
                              key={`paralegal-${paralegalSavings}`}
                              variants={pulseVariants}
                              whileInView="pulse"
                            >
                              €{paralegalSavings.toLocaleString()}
                            </motion.span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1 pl-7">
                            {paralegalMonthlyHours} hours × €{PARALEGAL_RATE}/hr
                          </div>
                        </div>
                      </div>

                      <div
                        className="border-l-4 pl-3 mb-3 transition-all duration-300 hover:translate-x-1"
                        style={{ borderColor: "#a855f7" }}
                      >
                        <div className="p-3 sm:p-4 bg-gradient-to-br from-white to-pink-50/30 backdrop-blur-sm rounded-lg border border-pink-100 shadow-sm">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="w-5 h-5 text-gray-400 stroke-[1.5] mr-2"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                />
                              </svg>
                              <span
                                className="font-medium text-sm sm:text-base"
                                style={{ color: "#a855f7" }}
                              >
                                {t.roiCalculator.legalAdminAssistant}
                              </span>
                            </div>
                            <motion.span
                              className="text-xl sm:text-2xl font-bold"
                              style={{ color: "#a855f7" }}
                              key={`assistant-${assistantSavings}`}
                              variants={pulseVariants}
                              whileInView="pulse"
                            >
                              €{assistantSavings.toLocaleString()}
                            </motion.span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1 pl-7">
                            {assistantMonthlyHours} hours × €{ASSISTANT_RATE}/hr
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="text-center text-xs text-gray-500 mt-3 italic">
                      {t.roiCalculator.basedOnAverageRates}
                    </div>

                    {/* Email Report Button */}
                    <div className="mt-6 pt-4 border-t border-gray-100">
                      <motion.button
                        className="w-auto mx-auto flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white rounded-full bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 shadow-sm hover:shadow transition-all duration-200"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() =>
                          alert("Email functionality coming soon!")
                        }
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                        {t.roiCalculator.emailSavingsReport}
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Premium CTA Section */}
          <div className="mt-12 pt-10">
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_20px_60px_rgba(0,0,0,0.12)] transition-all duration-500 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-b from-blue-100/30 to-transparent rounded-full transform translate-x-24 -translate-y-24 opacity-70"></div>
              <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-t from-emerald-100/30 to-transparent rounded-full transform -translate-x-16 translate-y-16 opacity-70"></div>

              <div className="relative z-10 md:flex items-center justify-between">
                <div className="mb-6 md:mb-0 md:mr-8">
                  <h4 className="text-2xl font-bold text-gray-800 mb-2">
                    {t.roiCalculator.startSavingCta}
                  </h4>
                  <p className="text-gray-600">
                    {t.roiCalculator.noCreditCardRequired}
                  </p>
                </div>

                <div className="flex-shrink-0">
                  <motion.a
                    href="/login"
                    className="inline-block px-8 py-4 text-lg font-semibold text-white rounded-full shadow-md bg-[#0C1C2D] hover:bg-[#18293B] transition-all duration-200"
                    whileHover={{
                      scale: 1.03,
                      boxShadow:
                        "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                    }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {t.roiCalculator.getStarted}
                  </motion.a>
                </div>
              </div>
            </div>

            <div className="mt-6 text-center">
              <motion.a
                href="#pricing"
                className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors"
                whileHover={{
                  scale: 1.03,
                }}
                whileTap={{ scale: 0.98 }}
              >
                {t.roiCalculator.seeOurPricing}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
