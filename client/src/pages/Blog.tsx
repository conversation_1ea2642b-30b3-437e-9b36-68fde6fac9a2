import { <PERSON> } from "wouter";
import { motion } from "framer-motion";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";

interface BlogPost {
  id: string;
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  slug: string;
}

const blogPosts: BlogPost[] = [
  {
    id: "1",
    title:
      "5 Essential Legal Research Techniques Every Solo Practitioner Should Master",
    summary:
      "Discover time-saving research methods that help you find relevant case law faster and build stronger arguments for your clients.",
    category: "Research Tips",
    readTime: "6 min read",
    publishDate: "May 20, 2025",
    author: "AiLex Team",
    slug: "essential-legal-research-techniques",
  },
  {
    id: "2",
    title: "How to Streamline Client Intake for Small Law Firms",
    summary:
      "Learn proven strategies to optimize your client onboarding process, reduce administrative burden, and improve client satisfaction.",
    category: "Practice Management",
    readTime: "8 min read",
    publishDate: "May 18, 2025",
    author: "AiLex Team",
    slug: "streamline-client-intake",
  },
  {
    id: "3",
    title: "Building a Profitable Solo Practice: Financial Management Tips",
    summary:
      "Essential financial strategies for solo attorneys, from setting rates to managing cash flow and planning for growth.",
    category: "Business Growth",
    readTime: "10 min read",
    publishDate: "May 15, 2025",
    author: "AiLex Team",
    slug: "building-profitable-solo-practice",
  },
];

export default function Blog() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />
      {/* Hero Section */}
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-8"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Home
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-center"
          >
            <Badge className="mb-6 bg-blue-100 text-blue-700 hover:bg-blue-200">
              <BookOpen className="w-4 h-4 mr-2" />
              Legal Insights
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Resources for Solo Practitioners & Small Firms
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Practical insights, proven strategies, and expert guidance to help
              you build a successful legal practice.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid gap-8 md:gap-10">
            {blogPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.12)] transition-all duration-300 p-8 border border-white/20"
              >
                <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-4">
                      <Badge
                        variant="outline"
                        className="text-blue-600 border-blue-200"
                      >
                        {post.category}
                      </Badge>
                      <div className="flex items-center text-sm text-gray-500 gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {post.publishDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {post.readTime}
                        </div>
                      </div>
                    </div>

                    <h2 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">
                      {post.title}
                    </h2>

                    <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                      {post.summary}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        By {post.author}
                      </span>
                    </div>
                  </div>

                  <div className="lg:ml-8">
                    <Link href={`/blog/${post.slug}`}>
                      <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white px-6 py-3 group">
                        Read Article
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-gradient-to-r from-[#0C1C2D] to-[#1a2b3d] rounded-2xl p-8 md:p-12 text-center text-white"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Transform Your Practice?
            </h3>
            <p className="text-blue-100 mb-8 text-lg max-w-2xl mx-auto">
              Join thousands of attorneys who are already using AiLex to
              streamline their legal research and grow their practice.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/login">
                <Button className="bg-[#B8FF5C] hover:bg-[#B8FF5C]/90 text-[#0C1C2D] font-semibold px-8 py-3">
                  Start Your Free Trial
                </Button>
              </Link>
              <Link
                href="/"
                className="inline-flex items-center text-blue-100 hover:text-white transition-colors"
              >
                <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                Back to Home
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
