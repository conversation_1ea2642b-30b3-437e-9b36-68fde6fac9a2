import { Link } from "wouter";
import { motion } from "framer-motion";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";


interface BlogPost {
  id: string;
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  slug: string;
}

const belgiumBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "5 Techniques Essentielles de Recherche Juridique pour les Praticiens Belges",
    summary:
      "Découvrez des méthodes de recherche qui font gagner du temps, spécifiques au droit belge, pour trouver plus rapidement la jurisprudence pertinente et construire des arguments plus solides.",
    category: "Conseils de Recherche",
    readTime: "6 min de lecture",
    publishDate: "20 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "techniques-recherche-juridique-belges-essentielles",
  },
  {
    id: "2",
    title: "Comment Optimiser l'Accueil Client pour les Cabinets d'Avocats Belges",
    summary:
      "Apprenez des stratégies éprouvées pour optimiser votre processus d'intégration client en Belgique, réduire la charge administrative et améliorer la satisfaction client.",
    category: "Gestion de Cabinet",
    readTime: "8 min de lecture",
    publishDate: "18 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "optimiser-accueil-client-cabinets-belges",
  },
  {
    id: "3",
    title:
      "Construire un Cabinet Rentable en Belgique : Conseils de Gestion Financière",
    summary:
      "Stratégies financières essentielles pour les avocats belges, de la fixation des tarifs à la gestion des flux de trésorerie et la planification de croissance sur le marché belge.",
    category: "Croissance d'Entreprise",
    readTime: "10 min de lecture",
    publishDate: "15 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "cabinet-rentable-belgique",
  },
  {
    id: "4",
    title: "Comprendre la Réglementation des Technologies Juridiques Belges",
    summary:
      "Naviguez dans le paysage réglementaire des technologies juridiques en Belgique, y compris les exigences de protection des données et de confidentialité client.",
    category: "Tech Juridique",
    readTime: "7 min de lecture",
    publishDate: "12 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "reglementation-tech-juridique-belge",
  },
  {
    id: "5",
    title: "Révision de Documents Assistée par IA : Une Perspective Belge",
    summary:
      "Comment l'intelligence artificielle transforme les processus de révision de documents pour les cabinets d'avocats belges tout en maintenant la conformité avec les réglementations locales.",
    category: "IA & Innovation",
    readTime: "9 min de lecture",
    publishDate: "10 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "revision-documents-ia-belgique",
  },
  {
    id: "6",
    title: "Pratique Juridique Multilingue en Belgique : Meilleures Pratiques",
    summary:
      "Gérer la pratique juridique en néerlandais, français et allemand en Belgique, y compris les stratégies de traduction et de communication.",
    category: "Gestion de Cabinet",
    readTime: "8 min de lecture",
    publishDate: "8 mai 2025",
    author: "Équipe AiLex Belgique",
    slug: "pratique-multilingue-belgique",
  },
];

export default function BelgiumBlog() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar currentState="BE" language="fr" />

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <BookOpen className="h-8 w-8 text-[#0C1C2D]" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Blog AiLex Belgique
              </h1>
            </div>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Perspectives, conseils et stratégies pour les professionnels du droit belges.
              Restez à la pointe des dernières technologies juridiques et de la gestion de cabinet.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {belgiumBlogPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800"
                    >
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>

                  <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#0C1C2D] transition-colors">
                    {post.title}
                  </h2>

                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.summary}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      {post.publishDate}
                    </div>
                    <Link href={`/be/blog/${post.slug}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#0C1C2D] hover:bg-[#0C1C2D] hover:text-white group"
                      >
                        Read More
                        <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

    </div>
  );
}
