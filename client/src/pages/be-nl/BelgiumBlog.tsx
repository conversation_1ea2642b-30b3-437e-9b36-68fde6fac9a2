import { Link } from "wouter";
import { motion } from "framer-motion";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";


interface BlogPost {
  id: string;
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  slug: string;
}

const belgiumBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "5 Essentiële Juridische Onderzoekstechnieken voor Belgische Praktijkvoerders",
    summary:
      "Ontdek tijdbesparende onderzoeksmethoden specifiek voor Belgisch recht die u helpen relevante jurisprudentie sneller te vinden en sterkere argumenten op te bouwen.",
    category: "Onderzoekstips",
    readTime: "6 min lezen",
    publishDate: "20 mei 2025",
    author: "AiLex België Team",
    slug: "essentiële-belgische-juridische-onderzoekstechnieken",
  },
  {
    id: "2",
    title: "Hoe Cliëntopname Stroomlijnen voor Belgische Advocatenkantoren",
    summary:
      "Leer bewezen strategieën om uw cliënt onboarding proces in België te optimaliseren, administratieve lasten te verminderen en cliënttevredenheid te verbeteren.",
    category: "Kantoormanagement",
    readTime: "8 min lezen",
    publishDate: "18 mei 2025",
    author: "AiLex België Team",
    slug: "stroomlijnen-belgische-cliëntopname",
  },
  {
    id: "3",
    title:
      "Een Winstgevende Praktijk Opbouwen in België: Tips voor Financieel Beheer",
    summary:
      "Essentiële financiële strategieën voor Belgische advocaten, van tariefstelling tot cashflow beheer en groeiplannen voor de Belgische markt.",
    category: "Bedrijfsgroei",
    readTime: "10 min lezen",
    publishDate: "15 mei 2025",
    author: "AiLex België Team",
    slug: "winstgevende-belgische-praktijk",
  },
  {
    id: "4",
    title: "Belgische Juridische Technologie Regelgeving Begrijpen",
    summary:
      "Navigeer door het regelgevingslandschap voor juridische technologie in België, inclusief gegevensbescherming en cliëntvertrouwelijkheidsvereisten.",
    category: "Juridische Tech",
    readTime: "7 min lezen",
    publishDate: "12 mei 2025",
    author: "AiLex België Team",
    slug: "belgische-juridische-tech-regelgeving",
  },
  {
    id: "5",
    title: "AI-Aangedreven Documentbeoordeling: Een Belgisch Perspectief",
    summary:
      "Hoe kunstmatige intelligentie documentbeoordelingsprocessen voor Belgische advocatenkantoren transformeert terwijl compliance met lokale regelgeving behouden blijft.",
    category: "AI & Innovatie",
    readTime: "9 min lezen",
    publishDate: "10 mei 2025",
    author: "AiLex België Team",
    slug: "ai-documentbeoordeling-belgië",
  },
  {
    id: "6",
    title: "Meertalige Juridische Praktijk in België: Beste Praktijken",
    summary:
      "Juridische praktijk beheren in het Nederlands, Frans en Duits in België, inclusief vertaal- en communicatiestrategieën.",
    category: "Kantoormanagement",
    readTime: "8 min lezen",
    publishDate: "8 mei 2025",
    author: "AiLex België Team",
    slug: "meertalige-praktijk-belgië",
  },
];

export default function BelgiumBlog() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar currentState="BE" language="nl" />

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <BookOpen className="h-8 w-8 text-[#0C1C2D]" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                AiLex België Blog
              </h1>
            </div>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Inzichten, tips en strategieën voor Belgische juridische professionals.
              Blijf voorop met de nieuwste ontwikkelingen in juridische technologie en kantoormanagement.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {belgiumBlogPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800"
                    >
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>

                  <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#0C1C2D] transition-colors">
                    {post.title}
                  </h2>

                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.summary}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      {post.publishDate}
                    </div>
                    <Link href={`/be/blog/${post.slug}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#0C1C2D] hover:bg-[#0C1C2D] hover:text-white group"
                      >
                        Read More
                        <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

    </div>
  );
}
