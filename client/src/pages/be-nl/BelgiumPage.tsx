import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SocialProof from "@/components/SocialProof";
import ProblemSolution from "@/components/ProblemSolution";
import FeatureTrio from "@/components/FeatureTrio";
import RoiCalculator from "@/components/RoiCalculator";
import Testimonials from "@/components/Testimonials";
import IndustryInsights from "@/components/IndustryInsights";
import Pricing from "@/components/Pricing";
import SecurityBanner from "@/components/SecurityBanner";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";
import { getTranslations } from "@/utils/translations";


export default function BelgiumPage() {
  // Get Dutch translations
  const t = getTranslations('nl');

  // Paginatitel instellen voor België
  useEffect(() => {
    document.title = t.pageTitle;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', t.metaDescription);
    } else {
      const meta = document.createElement('meta');
      meta.name = 'description';
      meta.content = t.metaDescription;
      document.head.appendChild(meta);
    }
  }, [t]);

  const belgiumContent = {
    heroTitle: t.heroTitle,
    heroSubtitle: t.heroSubtitle,
  };

  return (
    <div className="relative overflow-x-hidden">
      <Navbar currentState="BE" language="nl" />
      <main>
        <Hero
          customTitle={belgiumContent.heroTitle}
          customSubtitle={belgiumContent.heroSubtitle}
          state="BE"
        />
        <SocialProof />
        <ProblemSolution />
        <FeatureTrio state="BE" />
        <RoiCalculator />
        <IndustryInsights />
        <Testimonials defaultState="BE" />
        <Pricing />
        <SecurityBanner />
      </main>
      <Footer />
      <CookieNotice />


    </div>
  );
}
