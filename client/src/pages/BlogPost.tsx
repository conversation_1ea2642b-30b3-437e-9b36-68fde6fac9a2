import { <PERSON>, useRoute } from "wouter";
import { motion } from "framer-motion";
import {
  <PERSON>,
  Clock,
  ArrowLeft,
  BookOpen,
  CheckCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";

interface BlogPostData {
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  content: React.ReactNode;
}

const blogPostsData: Record<string, BlogPostData> = {
  "essential-legal-research-techniques": {
    title:
      "5 Essential Legal Research Techniques Every Solo Practitioner Should Master",
    summary:
      "Discover time-saving research methods that help you find relevant case law faster and build stronger arguments for your clients.",
    category: "Research Tips",
    readTime: "6 min read",
    publishDate: "May 20, 2025",
    author: "AiLex Team",
    content: (
      <div className="prose prose-lg max-w-none">
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          Effective legal research is the backbone of successful legal practice.
          Whether you're a solo practitioner or part of a small firm, mastering
          these five research techniques will help you work more efficiently and
          build stronger cases for your clients.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          1. Start with Secondary Sources
        </h2>
        <p className="mb-6">
          Before diving into primary sources, begin your research with secondary
          sources like legal encyclopedias, law reviews, and treatises. These
          resources provide valuable context and can point you toward the most
          relevant primary sources.
        </p>
        <div className="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
          <h4 className="font-semibold text-blue-900 mb-2">Pro Tip:</h4>
          <p className="text-blue-800">
            Use ALR (American Law Reports) annotations to find comprehensive
            discussions of legal issues along with citations to relevant cases.
          </p>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          2. Master Boolean Search Techniques
        </h2>
        <p className="mb-6">
          Learning to use Boolean operators (AND, OR, NOT) effectively can
          dramatically improve your search results. Combine terms strategically
          to narrow or broaden your search as needed.
        </p>
        <ul className="space-y-3 mb-8">
          <li className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <span>Use quotes for exact phrases: "reasonable doubt"</span>
          </li>
          <li className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <span>Use proximity connectors: contract w/5 breach</span>
          </li>
          <li className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <span>
              Use wildcards for variations: employ* (finds employ, employed,
              employment)
            </span>
          </li>
        </ul>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          3. Use Citators to Verify Authority
        </h2>
        <p className="mb-6">
          Always verify that your cases are still good law using citators like
          Shepard's or KeyCite. This prevents the embarrassment of citing
          overruled cases and ensures your arguments are built on solid legal
          ground.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          4. Leverage AI-Powered Research Tools
        </h2>
        <p className="mb-6">
          Modern AI tools can help you identify relevant cases, statutes, and
          regulations more quickly than traditional methods. They can also help
          you understand complex legal concepts and identify potential
          arguments.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          5. Create and Maintain Research Templates
        </h2>
        <p className="mb-6">
          Develop standardized research templates for common legal issues in
          your practice area. This ensures consistency and helps you avoid
          missing important research steps.
        </p>

        <div className="bg-gray-50 rounded-lg p-8 mt-12">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Ready to Streamline Your Legal Research?
          </h3>
          <p className="text-gray-600 mb-6">
            AiLex combines all these research techniques into one powerful
            platform, helping you find relevant cases and build stronger
            arguments faster than ever before.
          </p>
          <Link href="/">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Try AiLex Free for 14 Days
            </Button>
          </Link>
        </div>
      </div>
    ),
  },
  "streamline-client-intake": {
    title: "How to Streamline Client Intake for Small Law Firms",
    summary:
      "Learn proven strategies to optimize your client onboarding process, reduce administrative burden, and improve client satisfaction.",
    category: "Practice Management",
    readTime: "8 min read",
    publishDate: "May 18, 2025",
    author: "AiLex Team",
    content: (
      <div className="prose prose-lg max-w-none">
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          A well-designed client intake process sets the foundation for
          successful attorney-client relationships. Here's how to create an
          efficient system that saves time while providing excellent client
          service.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Create a Standardized Intake Form
        </h2>
        <p className="mb-6">
          Develop comprehensive intake forms that capture all necessary client
          information upfront. This reduces back-and-forth communication and
          ensures you have everything needed to evaluate the case.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Implement Digital Document Collection
        </h2>
        <p className="mb-6">
          Use secure client portals to collect documents electronically. This
          speeds up the process and creates an organized digital file from day
          one.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Set Clear Expectations Early
        </h2>
        <p className="mb-6">
          Provide detailed information about your process, fees, and timeline
          during the initial consultation. This prevents misunderstandings and
          builds trust.
        </p>

        <div className="bg-gray-50 rounded-lg p-8 mt-12">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Optimize Your Practice with AiLex
          </h3>
          <p className="text-gray-600 mb-6">
            Streamline every aspect of your legal practice with AI-powered tools
            designed specifically for solo practitioners and small firms.
          </p>
          <Link href="/login">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Start Your Free Trial
            </Button>
          </Link>
        </div>
      </div>
    ),
  },
  "building-profitable-solo-practice": {
    title: "Building a Profitable Solo Practice: Financial Management Tips",
    summary:
      "Essential financial strategies for solo attorneys, from setting rates to managing cash flow and planning for growth.",
    category: "Business Growth",
    readTime: "10 min read",
    publishDate: "May 15, 2025",
    author: "AiLex Team",
    content: (
      <div className="prose prose-lg max-w-none">
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          Building a profitable solo practice requires more than legal
          expertise—it demands smart financial management. Here are the key
          strategies that successful solo practitioners use to grow their
          practice.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Set Competitive Yet Profitable Rates
        </h2>
        <p className="mb-6">
          Research market rates in your area and practice type. Consider
          value-based pricing for routine matters and track your time
          meticulously to understand your true costs.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Manage Cash Flow Effectively
        </h2>
        <p className="mb-6">
          Implement clear payment terms, require retainers for new matters, and
          follow up on outstanding invoices promptly. Consider offering payment
          plans for larger matters.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-6">
          Invest in Technology That Pays for Itself
        </h2>
        <p className="mb-6">
          Choose legal technology that demonstrably improves efficiency and
          client service. Calculate the ROI of each tool to ensure it
          contributes to profitability.
        </p>

        <div className="bg-gray-50 rounded-lg p-8 mt-12">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Calculate Your ROI with AiLex
          </h3>
          <p className="text-gray-600 mb-6">
            See how much time and money you can save with AI-powered legal
            research and document drafting tools.
          </p>
          <Link href="/">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Try Our ROI Calculator
            </Button>
          </Link>
        </div>
      </div>
    ),
  },
};

export default function BlogPost() {
  const [, params] = useRoute("/blog/:slug");
  const slug = params?.slug;

  if (!slug || !blogPostsData[slug]) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Article Not Found
          </h1>
          <p className="text-gray-600 mb-8">
            The article you're looking for doesn't exist.
          </p>
          <Link href="/blog">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const post = blogPostsData[slug];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />
      {/* Header */}
      <div className="pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Link href="/blog">
              <Button
                variant="ghost"
                className="mb-8 p-0 h-auto text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Button>
            </Link>

            <div className="flex items-center gap-4 mb-6">
              <Badge
                variant="outline"
                className="text-blue-600 border-blue-200"
              >
                {post.category}
              </Badge>
              <div className="flex items-center text-sm text-gray-500 gap-4">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {post.publishDate}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {post.readTime}
                </div>
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {post.title}
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {post.summary}
            </p>

            <div className="text-sm text-gray-500 mb-12">By {post.author}</div>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 md:p-12 border border-white/20"
          >
            {post.content}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
