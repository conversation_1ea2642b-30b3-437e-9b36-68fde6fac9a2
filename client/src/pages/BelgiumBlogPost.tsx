import { <PERSON>, use<PERSON>out<PERSON> } from "wouter";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  ArrowLeft,
  BookOpen,
  CheckCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";

interface BlogPostData {
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  content: React.ReactNode;
}

// Belgian-specific blog posts data
const belgiumBlogPostsData: Record<string, BlogPostData> = {
  "essential-belgian-legal-research-techniques": {
    title: "5 Essential Legal Research Techniques for Belgian Practitioners",
    summary:
      "Discover time-saving research methods specific to Belgian law that help you find relevant case law faster and build stronger arguments.",
    category: "Research Tips",
    readTime: "6 min read",
    publishDate: "May 20, 2025",
    author: "AiLex Belgium Team",
    content: (
      <div className="prose prose-lg max-w-none">
        <p className="text-xl text-gray-600 mb-8">
          Legal research in Belgium requires understanding the unique structure
          of Belgian law, including federal, regional, and community
          legislation. Here are five essential techniques every Belgian
          practitioner should master.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          1. Master the Belgian Legal Hierarchy
        </h2>
        <p className="mb-6">
          Understanding the hierarchy of Belgian legal sources is crucial. Start
          with the Constitution, then federal laws, regional decrees, and local
          ordinances. Always check for the most recent versions and amendments.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          2. Utilize Multilingual Resources Effectively
        </h2>
        <p className="mb-6">
          Belgium's trilingual nature means legal documents exist in Dutch,
          French, and German. Learn to cross-reference between languages and
          understand regional variations in legal terminology.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          3. Navigate Regional Competencies
        </h2>
        <p className="mb-6">
          Different regions have different competencies. Understand which
          matters fall under federal, regional, or community jurisdiction to
          ensure you're researching the right legal framework.
        </p>

        <div className="bg-blue-50 border-l-4 border-blue-400 p-6 my-8">
          <div className="flex items-start">
            <CheckCircle className="h-6 w-6 text-blue-400 mt-1 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Pro Tip
              </h3>
              <p className="text-blue-800">
                Always verify the territorial application of laws and
                regulations, especially when dealing with regional matters like
                urban planning or environmental law.
              </p>
            </div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          4. Leverage Digital Legal Databases
        </h2>
        <p className="mb-6">
          Use specialized Belgian legal databases like Jura, Strada Lex, or the
          official government portals. These provide access to consolidated
          legislation, case law, and legal commentary.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          5. Stay Updated with Legal Monitoring
        </h2>
        <p className="mb-6">
          Belgian law changes frequently. Set up monitoring systems for relevant
          legal areas and subscribe to official bulletins and legal newsletters
          to stay current.
        </p>

        <div className="bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Ready to Streamline Your Research?
          </h3>
          <p className="text-gray-600 mb-4">
            AiLex's AI-powered research tools are specifically trained on
            Belgian legal sources, helping you find relevant information faster
            across all three national languages.
          </p>
          <Link href="/be">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Learn More About AiLex Belgium
            </Button>
          </Link>
        </div>
      </div>
    ),
  },
  "streamline-belgian-client-intake": {
    title: "How to Streamline Client Intake for Belgian Law Firms",
    summary:
      "Learn proven strategies to optimize your client onboarding process in Belgium, reduce administrative burden, and improve client satisfaction.",
    category: "Practice Management",
    readTime: "8 min read",
    publishDate: "May 18, 2025",
    author: "AiLex Belgium Team",
    content: (
      <div className="prose prose-lg max-w-none">
        <p className="text-xl text-gray-600 mb-8">
          Efficient client intake is crucial for Belgian law firms, especially
          when dealing with multilingual clients and complex regulatory
          requirements. Here's how to optimize your process.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          Understanding Belgian Client Expectations
        </h2>
        <p className="mb-6">
          Belgian clients expect clear communication in their preferred language
          and transparency about fees and procedures. Design your intake process
          to address these expectations from the start.
        </p>

        <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
          Multilingual Intake Forms
        </h2>
        <p className="mb-6">
          Create intake forms in Dutch, French, and German. Use clear, simple
          language and avoid legal jargon that might confuse clients. Consider
          using digital forms that can automatically translate or route to
          appropriate language-speaking staff.
        </p>

        <div className="bg-green-50 border-l-4 border-green-400 p-6 my-8">
          <div className="flex items-start">
            <CheckCircle className="h-6 w-6 text-green-400 mt-1 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                Best Practice
              </h3>
              <p className="text-green-800">
                Implement a digital intake system that can handle multiple
                languages and automatically route cases to attorneys with
                appropriate language skills and expertise.
              </p>
            </div>
          </div>
        </div>
      </div>
    ),
  },
};

export default function BelgiumBlogPost() {
  const [, params] = useRoute("/be/blog/:slug");
  const slug = params?.slug;

  if (!slug || !belgiumBlogPostsData[slug]) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Article Not Found
          </h1>
          <p className="text-gray-600 mb-8">
            The article you're looking for doesn't exist.
          </p>
          <Link href="/be/blog">
            <Button className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white">
              Back to Belgium Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const post = belgiumBlogPostsData[slug];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar currentState="BE" />
      {/* Header */}
      <div className="pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Link href="/be/blog">
              <Button
                variant="ghost"
                className="mb-8 p-0 h-auto text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Belgium Blog
              </Button>
            </Link>

            <div className="flex items-center gap-2 mb-4">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {post.category}
              </Badge>
              <div className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-1" />
                {post.readTime}
              </div>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {post.title}
            </h1>

            <div className="flex items-center text-gray-600 mb-8">
              <Calendar className="h-4 w-4 mr-2" />
              <span className="mr-4">{post.publishDate}</span>
              <span>By {post.author}</span>
            </div>

            <p className="text-xl text-gray-600 leading-relaxed">
              {post.summary}
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 md:p-12"
          >
            {post.content}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
