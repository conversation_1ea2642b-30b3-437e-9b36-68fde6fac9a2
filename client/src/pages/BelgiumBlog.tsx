import { <PERSON> } from "wouter";
import { motion } from "framer-motion";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from "@/components/Navbar";


interface BlogPost {
  id: string;
  title: string;
  summary: string;
  category: string;
  readTime: string;
  publishDate: string;
  author: string;
  slug: string;
}

const belgiumBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "5 Essential Legal Research Techniques for Belgian Practitioners",
    summary:
      "Discover time-saving research methods specific to Belgian law that help you find relevant case law faster and build stronger arguments.",
    category: "Research Tips",
    readTime: "6 min read",
    publishDate: "May 20, 2025",
    author: "AiLex Belgium Team",
    slug: "essential-belgian-legal-research-techniques",
  },
  {
    id: "2",
    title: "How to Streamline Client Intake for Belgian Law Firms",
    summary:
      "Learn proven strategies to optimize your client onboarding process in Belgium, reduce administrative burden, and improve client satisfaction.",
    category: "Practice Management",
    readTime: "8 min read",
    publishDate: "May 18, 2025",
    author: "AiLex Belgium Team",
    slug: "streamline-belgian-client-intake",
  },
  {
    id: "3",
    title:
      "Building a Profitable Practice in Belgium: Financial Management Tips",
    summary:
      "Essential financial strategies for Belgian attorneys, from setting rates to managing cash flow and planning for growth in the Belgian market.",
    category: "Business Growth",
    readTime: "10 min read",
    publishDate: "May 15, 2025",
    author: "AiLex Belgium Team",
    slug: "profitable-belgian-practice",
  },
  {
    id: "4",
    title: "Understanding Belgian Legal Technology Regulations",
    summary:
      "Navigate the regulatory landscape for legal technology in Belgium, including data protection and client confidentiality requirements.",
    category: "Legal Tech",
    readTime: "7 min read",
    publishDate: "May 12, 2025",
    author: "AiLex Belgium Team",
    slug: "belgian-legal-tech-regulations",
  },
  {
    id: "5",
    title: "AI-Powered Document Review: A Belgian Perspective",
    summary:
      "How artificial intelligence is transforming document review processes for Belgian law firms while maintaining compliance with local regulations.",
    category: "AI & Innovation",
    readTime: "9 min read",
    publishDate: "May 10, 2025",
    author: "AiLex Belgium Team",
    slug: "ai-document-review-belgium",
  },
  {
    id: "6",
    title: "Multilingual Legal Practice in Belgium: Best Practices",
    summary:
      "Managing legal practice across Dutch, French, and German languages in Belgium, including translation and communication strategies.",
    category: "Practice Management",
    readTime: "8 min read",
    publishDate: "May 8, 2025",
    author: "AiLex Belgium Team",
    slug: "multilingual-practice-belgium",
  },
];

export default function BelgiumBlog() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar currentState="BE" />

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <BookOpen className="h-8 w-8 text-[#0C1C2D]" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                AiLex Belgium Blog
              </h1>
            </div>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Insights, tips, and strategies for Belgian legal professionals.
              Stay ahead with the latest in legal technology and practice
              management.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {belgiumBlogPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800"
                    >
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>

                  <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#0C1C2D] transition-colors">
                    {post.title}
                  </h2>

                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.summary}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      {post.publishDate}
                    </div>
                    <Link href={`/be/blog/${post.slug}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-[#0C1C2D] hover:bg-[#0C1C2D] hover:text-white group"
                      >
                        Read More
                        <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

    </div>
  );
}
