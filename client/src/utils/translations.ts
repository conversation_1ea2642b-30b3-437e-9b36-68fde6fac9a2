export type Language = "en" | "fr" | "nl";

export interface Translations {
  // Page metadata
  pageTitle: string;
  metaDescription: string;

  // Hero section
  heroTitle: string;
  heroSubtitle: string;
  heroCallToAction: string;
  heroSecondaryAction: string;
  heroTagline: string;
  heroBadges: {
    startImmediately: string;
    noSetupNeeded: string;
    noLearningCurve: string;
    aiReceptionist: string;
    jurisdictionResearch: string;
  };

  // Dashboard Preview
  dashboard: {
    title: string;
    firmLabel: string;
    readyStatus: string;
    activeMatters: string;
    weeklyIncrease: string;
    recentActivity: string;
    documentAnalysis: string;
    employmentContract: string;
    clientIntake: string;
    tenantDispute: string;
    upcoming: string;
    seeAll: string;
    filingDeadline: string;
    delvauxCase: string;
    courtHearing: string;
    justiceOfPeace: string;
    schedule: string;
    openDashboard: string;
  };

  // Navigation
  navFeatures: string;
  navPricing: string;
  navBlog: string;
  navFaq: string;
  navContact: string;
  navLogin: string;
  navStartTrial: string;

  // Social Proof - Practice Areas
  practiceAreas: {
    personalInjury: string;
    family: string;
    criminal: string;
    civilLitigation: string;
    employment: string;
    immigration: string;
    businessCorporate: string;
    intellectualProperty: string;
  };

  // Problem Solution Section
  problemTitle: string;
  problemSubtitle: string;
  problems: {
    unbilledTime: {
      title: string;
      description: string;
    };
    missedCalls: {
      title: string;
      description: string;
    };
    expensiveSoftware: {
      title: string;
      description: string;
    };
  };

  solutionTitle: string;
  solutionSubtitle: string;
  solutionDescription: string;
  solutionBadge: string;

  bigLawTechTitle: string;
  bigLawTechDescription: string;
  testimonialQuote: string;
  costSavings: string;
  enterpriseLegalTech: string;
  ailexTeamPlan: string;
  oneUser: string;
  twoUsers: string;
  price199: string;
  price99: string;

  // Solution section
  solutionSectionTitle: string;
  solutionSectionSubtitle: string;
  solutionSectionDescription: string;
  solutionSectionHighlight: string;
  solutionSectionBenefits: string;

  // Solution steps
  solutionSteps: {
    step1: string;
    step2: string;
    step3: string;
  };

  // Solution headlines
  solutionHeadlines: {
    headline1: string;
    headline2: string;
    headline3: string;
  };

  // Solution time savings
  solutionTimeSavings: {
    badge1: string;
    badge2: string;
    badge3: string;
  };

  // Solution descriptions
  solutionDescriptions: {
    description1: string;
    description2: string;
    description3: string;
  };

  // Solution demo client quote
  solutionClientQuote: string;

  // Solution demo testimonial quote
  solutionTestimonialQuote: string;

  // Solution demo case name
  solutionCaseName: string;

  // Solution demo AI prioritized label
  aiPrioritized: string;

  // Solution demo document names
  noticeOfDisputeDraft: string;

  // Solution demo status labels
  dueToday: string;

  // Solution demo file names
  medicalCertificate: string;

  // Solution demo task names
  setCourtDate: string;

  // Solution demo priority labels
  highPriority: string;

  // Solution demo document types
  responseLetter: string;

  // Solution demo AI suggestion labels
  aiSuggested: string;

  // Solution demo upload status labels
  uploaded: string;

  // Solution demo legal research labels
  legalResearchLabels: {
    cited17x: string;
    belgianLaw: string;
    draftClientMemo: string;
    civilCodeArt: string;
    relevant: string;
  };

  // Solution demo testimonial quotes
  organizationTestimonial: string;
  researchTestimonial: string;

  // Typewriter CTA phrases
  typewriterPhrases: {
    phrase1: string;
    phrase2: string;
    phrase3: string;
    phrase4: string;
  };
  typewriterSubtitle: string;

  // Solution demo UI labels
  solutionDemoLabels: {
    newClientCall: string;
    callHandledByAiLex: string;
    capturedAutomatically: string;
    voiceRecording: string;
    clientName: string;
    caseType: string;
    urgency: string;
    syncedToAiLex: string;
  };

  // Features
  featuresTitle: string;
  featuresSubtitle: string;
  featuresReady: string;
  featuresSeeAction: string;
  featuresTrainingRequired: string;
  featuresEasyBreezyReady: string;
  featuresCoreFeatures: string;
  featuresMainTitle: string;

  features: {
    research: {
      title: string;
      description: string;
      demoTitle: string;
      demoContent: {
        title: string;
        personalInjuryLabel: string;
        personalInjuryText: string;
        keyPrecedentLabel: string;
        keyPrecedentText: string;
        relatedCasesLabel: string;
        relatedCasesText: string;
      };
    };
    drafting: {
      title: string;
      description: string;
      demoTitle: string;
      demoContent: {
        title: string;
        petitionText: string;
        aiSuggestionLabel: string;
        aiSuggestionText: string;
      };
    };
    receptionist: {
      title: string;
      description: string;
      demoTitle: string;
      sampleConversationLabel: string;
      sampleConversation: {
        client: string;
        ailex: string;
      };
    };
    caseOrganizer: {
      title: string;
      description: string;
      demoTitle: string;
      demoContent: {
        caseTitle: string;
        filesLabel: string;
        filesDescription: string;
        nextDeadlineLabel: string;
        nextDeadlineDescription: string;
        latestUpdateLabel: string;
        latestUpdateDescription: string;
      };
    };
    workflow: {
      title: string;
      description: string;
      demoTitle: string;
      demoContent: {
        title: string;
        sendWelcomeEmail: string;
        createClientFile: string;
        scheduleFirstMeeting: string;
        collectSupportingDocuments: string;
        automaticReminder: string;
      };
    };
    deadlines: {
      title: string;
      description: string;
      demoTitle: string;
      demoContent: {
        title: string;
        responseToOpposingBrief: string;
        today: string;
        courtFilingDeadline: string;
        threeDays: string;
        clientMeeting: string;
        oneWeek: string;
        syncedWithCalendar: string;
      };
    };
  };

  // Pricing
  pricingTitle: string;
  pricingSubtitle: string;
  billingMonthly: string;
  billingAnnual: string;
  annualDiscount: string;

  pricingPlans: {
    solo: {
      name: string;
      subtitle: string;
      features: string[];
      cta: string;
    };
    team: {
      name: string;
      subtitle: string;
      features: string[];
      cta: string;
    };
    scale: {
      name: string;
      subtitle: string;
      features: string[];
      cta: string;
    };
  };

  // Footer
  footerTagline: string;
  footerProduct: string;
  footerResources: string;
  footerNewsletter: string;
  footerNewsletterDescription: string;
  footerEmailPlaceholder: string;
  footerSubscribe: string;
  footerCopyright: string;

  // Common UI
  learnMore: string;
  getStarted: string;
  contactSales: string;
  startFreeTrial: string;
  viewDemo: string;
  close: string;

  // Legal terminology
  legalTerms: {
    lawyer: string;
    attorney: string;
    jurist: string;
    legalAssistant: string;
    aiLegalAssistant: string;
    legalResearch: string;
    aiReceptionist: string;
    caseManagement: string;
    documentDrafting: string;
    clientIntake: string;
  };

  // ROI Calculator
  roiCalculator: {
    badge: string;
    title: string;
    subtitle: string;
    mainTitle: string;
    mainSubtitle: string;
    estimateYourSavings: string;
    useSlidersInstruction: string;
    lawyerTime: string;
    lawyerTimeSubtitle: string;
    juristTime: string;
    juristTimeSubtitle: string;
    legalAdminTime: string;
    legalAdminTimeSubtitle: string;
    monthlySavingsSummary: string;
    monthlyTimeSavings: string;
    estimatedValue: string;
    perMonth: string;
    hoursEveryWeek: string;
    roleByRoleBreakdown: string;
    lawyer: string;
    jurist: string;
    legalAdminAssistant: string;
    basedOnAverageRates: string;
    emailSavingsReport: string;
    startSavingCta: string;
    noCreditCardRequired: string;
    getStarted: string;
    seeOurPricing: string;
    currentSituation: string;
    withAilex: string;
    savings: string;
    hourlyRate: string;
    hoursPerWeek: string;
    calculate: string;
    results: {
      timeSaved: string;
      moneySaved: string;
      efficiency: string;
      perMonth: string;
      perYear: string;
    };
  };

  // Testimonials
  testimonials: {
    title: string;
    subtitle: string;
    quotes: {
      quote1: string;
      author1: string;
      title1: string;
      quote2: string;
      author2: string;
      title2: string;
      quote3: string;
      author3: string;
      title3: string;
    };
  };

  // Industry Insights
  industryInsights: {
    title: string;
    subtitle: string;
    stats: {
      stat1: {
        number: string;
        label: string;
      };
      stat2: {
        number: string;
        label: string;
      };
      stat3: {
        number: string;
        label: string;
      };
      stat4: {
        number: string;
        label: string;
      };
    };
  };

  // Security Banner
  security: {
    title: string;
    description: string;
    features: string[];
    learnMore: string;
  };

  // Cookie Notice
  cookies: {
    message: string;
    accept: string;
    decline: string;
    learnMore: string;
  };

  // FAQ
  faq: {
    title: string;
    questions: {
      q1: {
        question: string;
        answer: string;
      };
      q2: {
        question: string;
        answer: string;
      };
      q3: {
        question: string;
        answer: string;
      };
      q4: {
        question: string;
        answer: string;
      };
      q5: {
        question: string;
        answer: string;
      };
    };
  };

  // Blog
  blog: {
    title: string;
    subtitle: string;
    readMore: string;
    backToBlog: string;
    relatedPosts: string;
    categories: string;
    tags: string;
    publishedOn: string;
    author: string;
  };
}

export const translations: Record<Language, Translations> = {
  en: {
    // Page metadata
    pageTitle: "AiLex - AI Legal Associate for Belgian Lawyers",
    metaDescription:
      "AI-powered legal assistant built for Belgian law. Research, draft, and organize like a big law firm at solo practice prices.",

    // Hero section
    heroTitle:
      "Your AI Legal Assistant — Built for Belgian Law. Works While You're in Court.",
    heroSubtitle:
      "Research like a legal librarian, draft like a jurist, organize like an assistant — at solo-firm prices.",
    heroCallToAction: "Start free trial",
    heroSecondaryAction: "View demo",
    heroTagline: "AiLex can do it ALL for you.",
    heroBadges: {
      startImmediately: "Start immediately",
      noSetupNeeded: "No setup needed",
      noLearningCurve: "No learning curve",
      aiReceptionist: "AI receptionist",
      jurisdictionResearch: "Targeted research — Belgian law",
    },

    dashboard: {
      title: "AiLex Dashboard",
      firmLabel: "Firm: Van den Berg & Partners",
      readyStatus: "Ready",
      activeMatters: "Active Matters",
      weeklyIncrease: "+2 this week",
      recentActivity: "Recent Activity",
      documentAnalysis: "Document Analysis",
      employmentContract: "Employment Contract – Draft.pdf",
      clientIntake: "Client Intake",
      tenantDispute: "Daniel Delvaux (Tenant Dispute)",
      upcoming: "Upcoming",
      seeAll: "See all",
      filingDeadline: "Filing Deadline",
      delvauxCase: "May 15 – Delvaux v. Landlord",
      courtHearing: "Court Hearing",
      justiceOfPeace: "May 20 – Justice of the Peace, Brussels",
      schedule: "Schedule",
      openDashboard: "Open Dashboard",
    },

    // Navigation
    navFeatures: "Product",
    navPricing: "Pricing",
    navBlog: "Blog",
    navFaq: "FAQ",
    navContact: "Contact",
    navLogin: "Login",
    navStartTrial: "Start free trial",

    // Social Proof - Practice Areas
    practiceAreas: {
      personalInjury: "Personal Injury",
      family: "Family",
      criminal: "Criminal",
      civilLitigation: "Civil Litigation",
      employment: "Employment",
      immigration: "Immigration",
      businessCorporate: "Business / Corporate",
      intellectualProperty: "Intellectual Property",
    },

    // Problem Solution Section
    problemTitle: "The Solo Practice Problem",
    problemSubtitle: "Small firms face big challenges",
    problems: {
      unbilledTime: {
        title: "40% lawyer time is unbilled admin.",
        description:
          "The average solo attorney loses nearly half their potential billable hours to administrative tasks.",
      },
      missedCalls: {
        title: "Missed intake calls cost solos €7k+/yr.",
        description:
          "Every missed call is a potential client gone. Small firms without reception staff lose thousands annually.",
      },
      expensiveSoftware: {
        title: "Big-law software starts at €199/user.",
        description:
          "Enterprise legal solutions are priced for large firms, making them unaffordable for solo practitioners.",
      },
    },

    solutionTitle: "Never Redraft a Motion — AiLex Writes It for You",
    solutionSubtitle: "8-12 hrs saved/week",
    solutionDescription:
      "AiLex finds relevant cases, surfaces key statutes, and drafts memos or motions—like a junior lawyer, minus the overhead.",
    solutionBadge: "8-12 hrs saved/week",

    bigLawTechTitle: "Big-Law Tech, Solo-Firm Pricing",
    bigLawTechDescription:
      "AiLex delivers AI-powered tools you'd normally pay €199/user for — starting at just €99/month for 2 users.",
    testimonialQuote: "AiLex saved us over €5,000 this year.",
    costSavings: "75% cost savings",
    enterpriseLegalTech: "Enterprise Legal Tech",
    ailexTeamPlan: "AiLex Team Plan",
    oneUser: "1 User",
    twoUsers: "2 Users",
    price199: "€199",
    price99: "€99",

    // Solution section
    solutionSectionTitle: "With AiLex, Compete in the Big League",
    solutionSectionSubtitle: "Solution",
    solutionSectionDescription:
      "AiLex is your full-time assistant — answering calls, logging details, researching laws, and drafting documents, so you can",
    solutionSectionHighlight: "HAVE A LIFE.",
    solutionSectionBenefits:
      "More billable work. More clients. Less admin. More cases won.",

    solutionSteps: {
      step1: "Step 1: Capture the Call",
      step2: "Step 2: Organize the Case",
      step3: "Step 3: Draft the Work",
    },

    solutionHeadlines: {
      headline1: "Never Miss a Lead — AiLex Answers Every Call",
      headline2: "Never Miss a Deadline — AiLex Keeps Every Case on Track",
      headline3: "Never Redraft a Legal Document — AiLex Writes It for You",
    },

    solutionTimeSavings: {
      badge1: "3-5 hrs saved/week",
      badge2: "5-7 hrs saved/week",
      badge3: "8-12 hrs saved/week",
    },

    solutionDescriptions: {
      description1:
        "AiLex's AI Receptionist answers inbound calls instantly, captures the client's name, case type, urgency, and files everything into your dashboard.",
      description2:
        "AiLex auto-sorts your docs and deadlines, flags urgent items, and suggests next steps — so you always know what matters most.",
      description3:
        "AiLex finds relevant cases, highlights key statutes, and drafts memos or court submissions — like a jurist, without the overhead.",
    },

    solutionClientQuote:
      "Hello, this is Daniel. I'm looking for help with a tenancy dispute. I found your firm online and wanted to check if you handle landlord-tenant matters.",

    solutionTestimonialQuote:
      "Everything I needed was already logged when I opened the app.",

    solutionCaseName: "Daniel v. Landlord",

    aiPrioritized: "AI-Prioritized",

    noticeOfDisputeDraft: "Notice of Dispute Draft",

    dueToday: "Due Today",

    medicalCertificate: "Medical Certificate.pdf",

    setCourtDate: "Set Court Date",

    highPriority: "High Priority",

    responseLetter: "Response Letter",

    aiSuggested: "AI Suggested",

    uploaded: "Uploaded",

    organizationTestimonial:
      "I don't chase docs anymore. Everything's in place when I log in.",

    legalResearchLabels: {
      cited17x: "Cited 17x",
      belgianLaw: "Belgian Law",
      draftClientMemo: "Draft: Client Memo – Response to Claim",
      civilCodeArt: "Civil Code Art. 1728",
      relevant: "Relevant",
    },

    researchTestimonial: "It's like having my own research clerk.",

    typewriterPhrases: {
      phrase1: "AiLex answers your calls",
      phrase2: "AiLex answers your calls, organizes deadlines",
      phrase3:
        "AiLex answers your calls, organizes deadlines, drafts documents",
      phrase4:
        "AiLex answers your calls, organizes deadlines, drafts documents, and finds relevant case law",
    },
    typewriterSubtitle:
      "AiLex researches like a legal librarian, drafts like a jurist, and organizes like an assistant — all at solo-firm prices.",

    solutionDemoLabels: {
      newClientCall: "New Client Call",
      callHandledByAiLex: "Call Handled by AiLex",
      capturedAutomatically: "📡 Captured automatically by AI",
      voiceRecording: "Voice recording",
      clientName: "Client Name:",
      caseType: "Case Type:",
      urgency: "Urgency:",
      syncedToAiLex: "✅ Synced to AiLex",
    },

    // Features
    featuresTitle: "Everything You Need to Run Your Practice",
    featuresSubtitle:
      "From intake to resolution, AiLex handles the work that keeps you from practicing law.",
    featuresReady: "Ready",
    featuresSeeAction: "See it in action",
    featuresTrainingRequired:
      "Training required: 0 hours. 0 minutes. 0 seconds.",
    featuresEasyBreezyReady: "Easy. Breezy. Ready.",
    featuresCoreFeatures: "Core Features",
    featuresMainTitle: "What AiLex Does for Solo Lawyers & Small Firms",

    features: {
      research: {
        title: "Research Module",
        description:
          "AiLex uploads your cases, legal texts, or pleadings — then finds references, creates summaries, and extracts key arguments in seconds.",
        demoTitle: "Research Demo",
        demoContent: {
          title: "Research Query Results",
          personalInjuryLabel: "Personal Injury Statute:",
          personalInjuryText:
            "Belgian Civil Code Article 1382 – General liability for harm caused by fault",
          keyPrecedentLabel: "Key Precedent:",
          keyPrecedentText:
            "Court of Cassation (2019) – Shared fault applied in traffic accident",
          relatedCasesLabel: "Related Cases:",
          relatedCasesText:
            "Found 18 similar cases in Belgian courts over the past 5 years",
        },
      },
      drafting: {
        title: "Drafting Co-Pilot",
        description:
          "AiLex drafts procedural documents, pleadings, and correspondence — using jurisdiction-specific language and formatting.",
        demoTitle: "Drafting Demo",
        demoContent: {
          title: "Petition to Obtain Evidence",
          petitionText:
            "Comes now the Plaintiff, Thomas De Smet, through legal counsel, and respectfully requests the Court to order the opposing party to disclose relevant documents in accordance with Judicial Code Article 877.",
          aiSuggestionLabel: "AI Suggestion:",
          aiSuggestionText:
            "Add reference to Judicial Code Article 877 – Request for production of documents",
        },
      },
      receptionist: {
        title: "AI Receptionist",
        description:
          "With AiLex, you'll never miss a client call again. Our AI Receptionist answers, qualifies leads, and books appointments — 24/7.",
        demoTitle: "Voice Agent Demo",
        sampleConversationLabel: "Sample intake conversation:",
        sampleConversation: {
          client: "I need help with a traffic offense case.",
          ailex:
            "I understand you need assistance with a traffic violation. Could you tell me when the incident occurred?",
        },
      },
      caseOrganizer: {
        title: "Case Organizer",
        description:
          "AiLex keeps your cases organized — link files, notes, deadlines, and client updates in one secure workspace.",
        demoTitle: "Case Management Demo",
        demoContent: {
          caseTitle: "De Smet v. Dupont - Personal Injury",
          filesLabel: "Files (12)",
          filesDescription:
            "Medical certificates, accident photos, witness statements",
          nextDeadlineLabel: "Next Deadline",
          nextDeadlineDescription:
            "Court-ordered document exchange due in 5 days",
          latestUpdateLabel: "Latest Update:",
          latestUpdateDescription:
            "Client called — recovering well, ready for hearing",
        },
      },
      workflow: {
        title: "Task & Workflow Automation",
        description:
          "From intake to resolution, AiLex keeps you on track — assign tasks, track progress, and automate the admin work you used to do manually.",
        demoTitle: "Workflow Demo",
        demoContent: {
          title: "New Client Workflow",
          sendWelcomeEmail: "Send welcome email",
          createClientFile: "Create client file",
          scheduleFirstMeeting: "Schedule first meeting",
          collectSupportingDocuments: "Collect supporting documents",
          automaticReminder: "Automatic reminder sent to client ✓",
        },
      },
      deadlines: {
        title: "Deadlines & Reminders",
        description:
          "AiLex tracks deadlines, sends reminders, and syncs with your calendar — helping you stay compliant, efficient, and stress-free.",
        demoTitle: "Calendar Integration Demo",
        demoContent: {
          title: "Upcoming Deadlines",
          responseToOpposingBrief: "Response to Opposing Brief",
          today: "Today",
          courtFilingDeadline: "Court Filing Deadline",
          threeDays: "3 days",
          clientMeeting: "Client Meeting",
          oneWeek: "1 week",
          syncedWithCalendar: "📅 Synced with Google Calendar",
        },
      },
    },

    // Pricing
    pricingTitle: "Simple, Transparent Pricing",
    pricingSubtitle: "Choose the plan that fits your practice",
    billingMonthly: "Monthly",
    billingAnnual: "Annual",
    annualDiscount: "Save 15%",

    pricingPlans: {
      solo: {
        name: "Solo",
        subtitle: "LAWYER & ASSISTANT",
        features: [
          "Lawyer + 1 Free Assistant seat",
          "Choose 1 practice area (e.g. Civil, Family, Labor Law)",
          "50 research queries & 20 document drafts/month",
          "Client intake (Email + form)",
          "Task & deadline management",
          "Case management & Document upload",
          "➕ Add AI Receptionist for intake & scheduling (100 min/month) for €29/month",
          "➕ Add extra user for €29/month",
        ],
        cta: "Start free trial",
      },
      team: {
        name: "Team",
        subtitle: "SMALL FIRM",
        features: [
          "5 users included",
          "✅ Everything in SOLO, plus:",
          "Includes multiple core Belgian practice areas",
          "Unlimited research & drafts*",
          "AI Receptionist for intake & scheduling (250 mins/month)",
          "➕ Add extra user for €29/month",
        ],
        cta: "Start free trial",
      },
      scale: {
        name: "Scale",
        subtitle: "GROWING BUSINESSES",
        features: [
          "10 users included",
          "✅ Everything in TEAM, plus:",
          "AI Receptionist (500 mins/month)",
          "Custom API integration",
          "Dedicated onboarding & support",
          "Priority access to new jurisdictions",
          "➕ Add extra user for €29/month",
        ],
        cta: "Contact sales",
      },
    },

    // Footer
    footerTagline: "Built for small firms, powered by big tech.",
    footerProduct: "Product",
    footerResources: "Resources",
    footerNewsletter: "Get Weekly AI Tips",
    footerNewsletterDescription:
      "Join 2,000+ lawyers getting weekly insights on AI tools, practice management, and legal tech trends.",
    footerEmailPlaceholder: "Enter your email",
    footerSubscribe: "Subscribe",
    footerCopyright: "© 2024 AiLex. All rights reserved.",

    // Common UI
    learnMore: "Learn more",
    getStarted: "Get started",
    contactSales: "Contact sales",
    startFreeTrial: "Start free trial",
    viewDemo: "View demo",
    close: "Close",

    // Legal terminology
    legalTerms: {
      lawyer: "Lawyer",
      attorney: "Attorney",
      jurist: "Jurist",
      legalAssistant: "Legal Assistant",
      aiLegalAssistant: "AI Legal Assistant",
      legalResearch: "Legal Research",
      aiReceptionist: "AI Receptionist",
      caseManagement: "Case Management",
      documentDrafting: "Document Drafting",
      clientIntake: "Client Intake",
    },

    // ROI Calculator
    roiCalculator: {
      badge: "ROI Calculator",
      title: "Calculate Your ROI with AiLex",
      subtitle: "See how much time and money you could save",
      mainTitle: "Calculate Your Legal Team Savings",
      mainSubtitle:
        "See how much time and money AiLex can save your firm by automating legal admin work and reducing overhead.",
      estimateYourSavings: "Estimate Your Savings",
      useSlidersInstruction: "Use the sliders below to adjust time saved",
      lawyerTime: "Lawyer Time",
      lawyerTimeSubtitle:
        "How many hours per week do you spend on admin instead of billable work?",
      juristTime: "Jurist (Legal Advisor) Time",
      juristTimeSubtitle: "How many hours per week can AiLex save?",
      legalAdminTime: "Legal Admin Assistant Time",
      legalAdminTimeSubtitle: "How many hours per week can AiLex save?",
      monthlySavingsSummary: "Your Monthly Savings Summary",
      monthlyTimeSavings: "Monthly Time Savings",
      estimatedValue: "Estimated Value",
      perMonth: "per month",
      hoursEveryWeek: "hours every week",
      roleByRoleBreakdown: "Role-by-Role Breakdown",
      lawyer: "Lawyer",
      jurist: "Jurist (Legal Advisor)",
      legalAdminAssistant: "Legal Admin Assistant",
      basedOnAverageRates: "Based on average national hourly rates by role",
      emailSavingsReport: "Email me this savings report",
      startSavingCta: "Start saving with AiLex — Try free for 14 days",
      noCreditCardRequired:
        "No credit card required. Full access to all features.",
      getStarted: "Get Started",
      seeOurPricing: "See our pricing",
      currentSituation: "Current Situation",
      withAilex: "With AiLex",
      savings: "Your Savings",
      hourlyRate: "Your hourly rate (€)",
      hoursPerWeek: "Admin hours per week",
      calculate: "Calculate Savings",
      results: {
        timeSaved: "Time Saved",
        moneySaved: "Money Saved",
        efficiency: "Efficiency Gain",
        perMonth: "per month",
        perYear: "per year",
      },
    },

    // Testimonials
    testimonials: {
      title: "Trusted by Legal Professionals",
      subtitle: "See what lawyers are saying about AiLex",
      quotes: {
        quote1:
          "AiLex has transformed how we handle case research. What used to take hours now takes minutes.",
        author1: "Marie Dubois",
        title1: "Family Law Attorney, Brussels",
        quote2:
          "The AI receptionist never misses a call. Our client intake has increased by 40%.",
        author2: "Jan Vermeulen",
        title2: "Personal Injury Lawyer, Antwerp",
        quote3:
          "Finally, legal tech that actually understands Belgian law. Game changer for our practice.",
        author3: "Sophie Laurent",
        title3: "Civil Litigation Attorney, Ghent",
      },
    },

    // Industry Insights
    industryInsights: {
      title: "The Future of Legal Practice",
      subtitle: "AI is transforming how lawyers work",
      stats: {
        stat1: {
          number: "73%",
          label: "of lawyers report AI saves them 5+ hours per week",
        },
        stat2: {
          number: "€12,000",
          label: "average annual savings per lawyer using AI tools",
        },
        stat3: {
          number: "2.5x",
          label: "faster document drafting with AI assistance",
        },
        stat4: {
          number: "95%",
          label: "client satisfaction rate with AI-powered firms",
        },
      },
    },

    // Security Banner
    security: {
      title: "Enterprise-Grade Security",
      description:
        "Your client data is protected with bank-level security measures.",
      features: [
        "End-to-end encryption",
        "GDPR compliant",
        "SOC 2 certified",
        "Regular security audits",
      ],
      learnMore: "Learn more about our security",
    },

    // Cookie Notice
    cookies: {
      message:
        "We use cookies to enhance your experience and analyze site usage.",
      accept: "Accept",
      decline: "Decline",
      learnMore: "Learn more",
    },

    // FAQ
    faq: {
      title: "Frequently Asked Questions",
      questions: {
        q1: {
          question: "How does AiLex keep my data secure?",
          answer:
            "AiLex uses enterprise-grade security with end-to-end encryption, GDPR compliance, and regular security audits to protect your client data.",
        },
        q2: {
          question: "Can AiLex handle Belgian legal documents?",
          answer:
            "Yes, AiLex is specifically trained on Belgian law and can handle documents in French, Dutch, and English according to Belgian legal standards.",
        },
        q3: {
          question: "How much time can I save with AiLex?",
          answer:
            "Most lawyers save 8-12 hours per week on administrative tasks, research, and document drafting, allowing more time for billable work.",
        },
        q4: {
          question: "Is there a free trial?",
          answer:
            "Yes, we offer a 14-day free trial with full access to all features. No credit card required.",
        },
        q5: {
          question: "Can I cancel anytime?",
          answer:
            "Absolutely. You can cancel your subscription at any time with no cancellation fees or long-term commitments.",
        },
      },
    },

    // Blog
    blog: {
      title: "Legal AI Insights",
      subtitle: "Stay updated on the latest in legal technology and AI",
      readMore: "Read more",
      backToBlog: "Back to blog",
      relatedPosts: "Related posts",
      categories: "Categories",
      tags: "Tags",
      publishedOn: "Published on",
      author: "Author",
    },
  },

  fr: {
    // Page metadata
    pageTitle: "AiLex - Assistant Juridique IA pour Avocats Belges",
    metaDescription:
      "Assistant juridique alimenté par l'IA conçu pour le droit belge. Recherchez, rédigez et organisez comme un grand cabinet au prix d'un cabinet individuel.",

    // Hero section
    heroTitle:
      "Votre Assistant Juridique IA — Conçu pour le Droit Belge. Travaille Pendant que Vous Êtes au Tribunal.",
    heroSubtitle:
      "Recherche comme un documentaliste juridique, rédige comme un juriste, organise comme un assistant — aux tarifs d'un cabinet solo.",
    heroCallToAction: "Essayer AiLex Gratuitement",
    heroSecondaryAction: "Voir la démo",
    heroTagline: "AiLex fait TOUT cela pour vous.",
    heroBadges: {
      startImmediately: "Démarrage immédiat",
      noSetupNeeded: "Sans configuration",
      noLearningCurve: "Utilisation intuitive",
      aiReceptionist: "Réceptionniste IA",
      jurisdictionResearch: "Recherche ciblée — Droit belge",
    },

    dashboard: {
      title: "AiLex Tableau de bord",
      firmLabel: "Cabinet : Van den Berg & Partners",
      readyStatus: "Prêt",
      activeMatters: "DOSSIERS ACTIFS",
      weeklyIncrease: "+2 cette semaine",
      recentActivity: "ACTIVITÉ RÉCENTE",
      documentAnalysis: "Analyse de document",
      employmentContract: "Contrat de travail – Brouillon.pdf",
      clientIntake: "Nouveau client",
      tenantDispute: "Daniel Delvaux (Litige locatif)",
      upcoming: "À VENIR",
      seeAll: "Voir tout",
      filingDeadline: "Date limite de dépôt",
      delvauxCase: "15 mai – Delvaux c. Propriétaire",
      courtHearing: "Audience au tribunal",
      justiceOfPeace: "20 mai – Justice de Paix, Bruxelles",
      schedule: "Planifier",
      openDashboard: "Ouvrir le tableau de bord",
    },

    // Navigation
    navFeatures: "Produit",
    navPricing: "Prix",
    navBlog: "Blog",
    navFaq: "FAQ",
    navContact: "Contact",
    navLogin: "Se connecter",
    navStartTrial: "Essayer gratuitement",

    // Social Proof - Practice Areas
    practiceAreas: {
      personalInjury: "Dommages Corporels",
      family: "Droit de la Famille",
      criminal: "Droit Pénal",
      civilLitigation: "Contentieux Civil",
      employment: "Droit du Travail",
      immigration: "Droit de l'Immigration",
      businessCorporate: "Droit des Affaires",
      intellectualProperty: "Propriété Intellectuelle",
    },

    // Problem Solution Section
    problemTitle: "Le Problème des Cabinets Individuels",
    problemSubtitle: "Les petits cabinets font face à de grands défis",
    problems: {
      unbilledTime: {
        title: "40% du temps d'avocat est de l'admin non facturable.",
        description:
          "L'avocat individuel moyen perd près de la moitié de ses heures facturables potentielles aux tâches administratives.",
      },
      missedCalls: {
        title: "Les appels manqués coûtent +7k€/an aux solos.",
        description:
          "Chaque appel manqué est un client potentiel perdu. Les petits cabinets sans réceptionniste perdent des milliers d'euros annuellement.",
      },
      expensiveSoftware: {
        title:
          "Les logiciels de grands cabinets commencent à 199€/utilisateur.",
        description:
          "Les solutions juridiques d'entreprise sont tarifées pour les grands cabinets, les rendant inabordables pour les praticiens individuels.",
      },
    },

    solutionTitle:
      "Ne Rédigez Plus Jamais une Requête — AiLex l'Écrit pour Vous",
    solutionSubtitle: "8-12h économisées/semaine",
    solutionDescription:
      "AiLex trouve les affaires pertinentes, identifie les statuts clés, et rédige des mémos ou requêtes — comme un avocat junior, sans les frais généraux.",
    solutionBadge: "8-12h économisées/semaine",

    bigLawTechTitle:
      "Tech des grands cabinets, tarifs pour avocats indépendants",
    bigLawTechDescription:
      "AiLex vous propose des outils juridiques IA — normalement facturés 199 €/utilisateur — dès 99 €/mois pour 2 utilisateurs.",
    testimonialQuote:
      "AiLex nous a fait économiser plus de 5 000 € cette année.",
    costSavings: "Économies de 75 %",
    enterpriseLegalTech: "Tech des grands cabinets",
    ailexTeamPlan: "Plan AiLex Team",
    oneUser: "1 utilisateur",
    twoUsers: "2 utilisateurs",
    price199: "199 €",
    price99: "99 €",

    // Solution section
    solutionSectionTitle: "Avec AiLex, Jouez dans la Ligue des Grands",
    solutionSectionSubtitle: "Solution",
    solutionSectionDescription:
      "AiLex est votre assistant à plein temps — il répond aux appels, note les détails, recherche les lois et rédige les documents… pour que vous puissiez enfin",
    solutionSectionHighlight: "RESPIRER.",
    solutionSectionBenefits:
      "Plus de travail facturable. Plus de clients. Moins d'admin. Plus de dossiers gagnés.",

    solutionSteps: {
      step1: "Étape 1 : Appel transcrit",
      step2: "Étape 2 : Dossier organisé",
      step3: "Étape 3 : Travail rédigé",
    },

    solutionHeadlines: {
      headline1: "Ne ratez jamais un client — AiLex répond à tous vos appels",
      headline2:
        "Ne ratez jamais une échéance — AiLex garde vos dossiers à jour",
      headline3:
        "Ne rédigez plus jamais un document juridique — AiLex le fait pour vous",
    },

    solutionTimeSavings: {
      badge1: "3-5 h gagnées/semaine",
      badge2: "5-7 h gagnées/semaine",
      badge3: "8-12 h gagnées/semaine",
    },

    solutionDescriptions: {
      description1:
        "L'assistant vocal IA d'AiLex répond instantanément aux appels entrants, note le nom du client, le type de dossier, l'urgence, et enregistre tout dans votre tableau de bord.",
      description2:
        "AiLex organise automatiquement vos documents et échéances, identifie les urgences et propose les prochaines étapes — pour que rien ne vous échappe.",
      description3:
        "AiLex recherche la jurisprudence, met en avant les lois clés et rédige vos documents juridiques — comme un juriste, à moindre coût.",
    },

    solutionClientQuote:
      "Bonjour, ici Daniel. Je cherche de l'aide pour un litige locatif. J'ai trouvé votre cabinet en ligne et je voulais savoir si vous traitez ce type de dossier.",

    solutionTestimonialQuote:
      "Tout ce dont j'avais besoin était déjà enregistré quand j'ai ouvert l'application.",

    solutionCaseName: "Daniel c. Propriétaire",

    aiPrioritized: "Priorisé par l'IA",

    noticeOfDisputeDraft: "Avis de litige — Brouillon",

    dueToday: "À remettre aujourd'hui",

    medicalCertificate: "Certificat médical.pdf",

    setCourtDate: "Fixer la date d'audience",

    highPriority: "Priorité élevée",

    responseLetter: "Lettre de réponse",

    aiSuggested: "Suggéré par l'IA",

    uploaded: "Importé",

    organizationTestimonial:
      "Fini la chasse aux documents. Tout est prêt dès que je me connecte.",

    legalResearchLabels: {
      cited17x: "Cité 17x",
      belgianLaw: "Droit belge",
      draftClientMemo: "Brouillon : Note client — Réponse à la réclamation",
      civilCodeArt: "Code civil, art. 1728",
      relevant: "Pertinent",
    },

    researchTestimonial: "C'est comme avoir un juriste dédié à la recherche.",

    typewriterPhrases: {
      phrase1: "AiLex répond à vos appels",
      phrase2: "AiLex répond à vos appels, organise vos échéances",
      phrase3:
        "AiLex répond à vos appels, organise vos échéances, rédige vos documents",
      phrase4:
        "AiLex répond à vos appels, organise vos échéances, rédige vos documents et trouve la jurisprudence pertinente",
    },
    typewriterSubtitle:
      "AiLex fait vos recherches comme un documentaliste juridique, rédige comme un juriste et organise comme un assistant — le tout à prix d'indépendant.",

    solutionDemoLabels: {
      newClientCall: "Nouvel appel client",
      callHandledByAiLex: "Appel pris en charge par AiLex",
      capturedAutomatically: "Capté automatiquement par l'IA",
      voiceRecording: "Enregistrement vocal",
      clientName: "Nom du client :",
      caseType: "Type d'affaire :",
      urgency: "Urgence :",
      syncedToAiLex: "✅ Synchronisé avec AiLex",
    },

    // Features
    featuresTitle: "Tout ce dont Vous Avez Besoin pour Gérer Votre Cabinet",
    featuresSubtitle:
      "De l'accueil à la résolution, AiLex gère le travail qui vous empêche de pratiquer le droit.",
    featuresReady: "Prêt",
    featuresSeeAction: "Voir plus",
    featuresTrainingRequired:
      "Formation requise : 0 heure. 0 minute. 0 seconde.",
    featuresEasyBreezyReady: "Simple. Facile. Prêt à l'emploi.",
    featuresCoreFeatures: "Fonctionnalités Principales",
    featuresMainTitle: "Des Outils de Grands Cabinets — pour les Indépendants",

    features: {
      research: {
        title: "Module de Recherche",
        description:
          "AiLex charge vos dossiers, textes juridiques ou conclusions — puis trouve les références, génère des résumés et extrait les arguments clés en quelques secondes.",
        demoTitle: "Démo Recherche",
        demoContent: {
          title: "Résultats de la recherche juridique",
          personalInjuryLabel:
            "Disposition applicable (responsabilité civile) :",
          personalInjuryText:
            "Code civil belge, article 1382 – Responsabilité générale pour faute ayant causé un dommage",
          keyPrecedentLabel: "Jurisprudence clé :",
          keyPrecedentText:
            "Cour de cassation (2019) – Application de la faute partagée dans un accident de la circulation",
          relatedCasesLabel: "Affaires connexes :",
          relatedCasesText:
            "18 affaires similaires trouvées dans les tribunaux belges au cours des 5 dernières années",
        },
      },
      drafting: {
        title: "Copilote de Rédaction",
        description:
          "AiLex rédige des documents procéduraux, plaidoiries et correspondances — en utilisant le langage et le format propre à la juridiction.",
        demoTitle: "Démo Rédaction",
        demoContent: {
          title: "Requête en obtention de preuve",
          petitionText:
            "Le demandeur, Thomas De Smet, représenté par son conseil, demande au tribunal d'enjoindre la partie adverse de produire les documents pertinents, conformément à l'article 877 du Code judiciaire.",
          aiSuggestionLabel: "Suggestion IA :",
          aiSuggestionText:
            "Ajouter une référence à l'article 877 du Code judiciaire – Demande de production de documents",
        },
      },
      receptionist: {
        title: "Réceptionniste IA",
        description:
          "Avec AiLex, vous ne manquerez plus jamais un appel client. Notre Réceptionniste IA répond, qualifie les prospects et prend des rendez-vous — 24h/24, 7j/7.",
        demoTitle: "Démo Agent Vocal",
        sampleConversationLabel:
          "Exemple de conversation d'ouverture de dossier:",
        sampleConversation: {
          client: "J'ai besoin d'aide pour une infraction au code de la route.",
          ailex:
            "Je comprends que vous avez besoin d'assistance pour une infraction au code de la route. Pourriez-vous me dire quand l'incident a eu lieu ?",
        },
      },
      caseOrganizer: {
        title: "Gestionnaire de Dossiers",
        description:
          "AiLex garde vos dossiers organisés — avec vos fichiers, notes, échéances et communications client regroupés dans un espace sécurisé.",
        demoTitle: "Démo Gestion d'Affaires",
        demoContent: {
          caseTitle: "De Smet c. Dupont - Dommages corporels",
          filesLabel: "Fichiers (12)",
          filesDescription:
            "Certificats médicaux, photos d'accident, témoignages",
          nextDeadlineLabel: "Prochaine échéance",
          nextDeadlineDescription:
            "Échange de documents ordonné par le tribunal dans 5 jours",
          latestUpdateLabel: "Dernière mise à jour :",
          latestUpdateDescription:
            "Client appelé — se rétablit bien, prêt pour l'audience",
        },
      },
      workflow: {
        title: "Automatisation des tâches et processus",
        description:
          "De l'ouverture à la résolution du dossier, AiLex structure votre travail : assigne les tâches, suit les progrès et automatise les tâches administratives manuelles.",
        demoTitle: "Démo Flux de Travail",
        demoContent: {
          title: "Flux de travail nouveau client",
          sendWelcomeEmail: "Envoyer l'e-mail de bienvenue",
          createClientFile: "Créer le dossier client",
          scheduleFirstMeeting: "Planifier le premier rendez-vous",
          collectSupportingDocuments: "Collecter les documents justificatifs",
          automaticReminder: "Rappel automatique envoyé au client ✓",
        },
      },
      deadlines: {
        title: "Échéances et Rappels",
        description:
          "AiLex suit vos échéances, envoie des rappels et se synchronise avec votre agenda — pour une gestion fluide et sans stress.",
        demoTitle: "Démo Intégration Calendrier",
        demoContent: {
          title: "Échéances à venir",
          responseToOpposingBrief: "Réponse aux conclusions adverses",
          today: "Aujourd'hui",
          courtFilingDeadline: "Délai de dépôt au tribunal",
          threeDays: "3 jours",
          clientMeeting: "Rendez-vous client",
          oneWeek: "1 semaine",
          syncedWithCalendar: "📅 Synchronisé avec Google Agenda",
        },
      },
    },

    // Pricing
    pricingTitle: "Prix Clairs et Transparents",
    pricingSubtitle: "Choisissez le plan qui convient à votre cabinet",
    billingMonthly: "Mensuel",
    billingAnnual: "Annuel",
    annualDiscount: "Économisez 15%",

    pricingPlans: {
      solo: {
        name: "Solo",
        subtitle: "AVOCAT & ASSISTANT",
        features: [
          "Avocat + 1 siège Assistant gratuit",
          "Choisissez 1 domaine de pratique (ex. Civil, Famille, Droit du Travail)",
          "50 requêtes de recherche & 20 rédactions de documents/mois",
          "Accueil client (Email + formulaire)",
          "Gestion des tâches et échéances",
          "Gestion d'affaires & Téléchargement de documents",
          "➕ Ajoutez Réceptionniste IA pour accueil & planification (100 min/mois) pour 29€/mois",
          "➕ Ajoutez utilisateur supplémentaire pour 29€/mois",
        ],
        cta: "Commencer l'essai gratuit",
      },
      team: {
        name: "Équipe",
        subtitle: "PETIT CABINET",
        features: [
          "5 utilisateurs inclus",
          "✅ Tout dans SOLO, plus :",
          "Inclut plusieurs domaines de pratique belges principaux",
          "Recherches et rédactions illimitées*",
          "Réceptionniste IA pour accueil & planification (250 mins/mois)",
          "➕ Ajoutez utilisateur supplémentaire pour 29€/mois",
        ],
        cta: "Commencer l'essai gratuit",
      },
      scale: {
        name: "Échelle",
        subtitle: "ENTREPRISES EN CROISSANCE",
        features: [
          "10 utilisateurs inclus",
          "✅ Tout dans ÉQUIPE, plus :",
          "Réceptionniste IA (500 mins/mois)",
          "Intégration API personnalisée",
          "Intégration et support dédiés",
          "Accès prioritaire aux nouvelles juridictions",
          "➕ Ajoutez utilisateur supplémentaire pour 29€/mois",
        ],
        cta: "Contacter les ventes",
      },
    },

    // Footer
    footerTagline:
      "Conçu pour les petits cabinets, alimenté par la grande technologie.",
    footerProduct: "Produit",
    footerResources: "Ressources",
    footerNewsletter: "Recevez des Conseils IA Hebdomadaires",
    footerNewsletterDescription:
      "Rejoignez 2 000+ avocats recevant des insights hebdomadaires sur les outils IA, la gestion de cabinet et les tendances tech juridiques.",
    footerEmailPlaceholder: "Entrez votre email",
    footerSubscribe: "S'abonner",
    footerCopyright: "© 2024 AiLex. Tous droits réservés.",

    // Common UI
    learnMore: "En savoir plus",
    getStarted: "Commencer",
    contactSales: "Contacter les ventes",
    startFreeTrial: "Commencer l'essai gratuit",
    viewDemo: "Voir la démo",
    close: "Fermer",

    // Legal terminology
    legalTerms: {
      lawyer: "Avocat",
      attorney: "Avocat",
      jurist: "Juriste",
      legalAssistant: "Assistant Juridique",
      aiLegalAssistant: "Assistant Juridique IA",
      legalResearch: "Recherche Juridique",
      aiReceptionist: "Réceptionniste IA",
      caseManagement: "Gestion d'Affaires",
      documentDrafting: "Rédaction de Documents",
      clientIntake: "Accueil Client",
    },

    // ROI Calculator
    roiCalculator: {
      badge: "Calculateur de Rentabilité",
      title: "Calculez Votre ROI avec AiLex",
      subtitle: "Voyez combien de temps et d'argent vous pourriez économiser",
      mainTitle: "Vos économies en un clic.",
      mainSubtitle:
        "Calculez ce que votre cabinet économise chaque mois avec AiLex, en automatisant les tâches juridiques répétitives et en réduisant vos coûts fixes.",
      estimateYourSavings: "Estimez Vos Économies",
      useSlidersInstruction:
        "Utilisez les curseurs ci-dessous pour ajuster le temps économisé",
      lawyerTime: "Temps d'Avocat",
      lawyerTimeSubtitle:
        "Combien d'heures par semaine consacrez-vous à l'administration au lieu du travail facturable ?",
      juristTime: "Temps de Juriste (Conseiller Juridique)",
      juristTimeSubtitle:
        "Combien d'heures par semaine AiLex peut-il économiser ?",
      legalAdminTime: "Temps d'Assistant Administratif Juridique",
      legalAdminTimeSubtitle:
        "Combien d'heures par semaine AiLex peut-il économiser ?",
      monthlySavingsSummary: "Résumé de Vos Économies Mensuelles",
      monthlyTimeSavings: "Économies de Temps Mensuelles",
      estimatedValue: "Valeur Estimée",
      perMonth: "par mois",
      hoursEveryWeek: "heures chaque semaine",
      roleByRoleBreakdown: "Répartition par Rôle",
      lawyer: "Avocat",
      jurist: "Juriste (Conseiller Juridique)",
      legalAdminAssistant: "Assistant Administratif Juridique",
      basedOnAverageRates:
        "Basé sur les tarifs horaires nationaux moyens par rôle",
      emailSavingsReport: "Envoyez-moi ce rapport d'économies par e-mail",
      startSavingCta:
        "Commencez à économiser avec AiLex — Essai gratuit de 14 jours",
      noCreditCardRequired:
        "Aucune carte de crédit requise. Accès complet à toutes les fonctionnalités.",
      getStarted: "Commencer",
      seeOurPricing: "Voir nos tarifs",
      currentSituation: "Situation Actuelle",
      withAilex: "Avec AiLex",
      savings: "Vos Économies",
      hourlyRate: "Votre taux horaire (€)",
      hoursPerWeek: "Heures d'admin par semaine",
      calculate: "Calculer les Économies",
      results: {
        timeSaved: "Temps Économisé",
        moneySaved: "Argent Économisé",
        efficiency: "Gain d'Efficacité",
        perMonth: "par mois",
        perYear: "par an",
      },
    },

    // Testimonials
    testimonials: {
      title: "Approuvé par les Professionnels du Droit",
      subtitle: "Découvrez ce que les avocats disent d'AiLex",
      quotes: {
        quote1:
          "AiLex a transformé notre façon de gérer la recherche d'affaires. Ce qui prenait des heures ne prend maintenant que des minutes.",
        author1: "Marie Dubois",
        title1: "Avocate en Droit de la Famille, Bruxelles",
        quote2:
          "Le réceptionniste IA ne manque jamais un appel. Notre accueil client a augmenté de 40%.",
        author2: "Jan Vermeulen",
        title2: "Avocat en Dommages Corporels, Anvers",
        quote3:
          "Enfin, une technologie juridique qui comprend vraiment le droit belge. Révolutionnaire pour notre cabinet.",
        author3: "Sophie Laurent",
        title3: "Avocate en Contentieux Civil, Gand",
      },
    },

    // Industry Insights
    industryInsights: {
      title: "L'Avenir de la Pratique Juridique",
      subtitle: "L'IA transforme la façon dont les avocats travaillent",
      stats: {
        stat1: {
          number: "73%",
          label:
            "des avocats rapportent que l'IA leur fait économiser 5+ heures par semaine",
        },
        stat2: {
          number: "12 000€",
          label:
            "économies annuelles moyennes par avocat utilisant des outils IA",
        },
        stat3: {
          number: "2,5x",
          label: "rédaction de documents plus rapide avec l'assistance IA",
        },
        stat4: {
          number: "95%",
          label:
            "taux de satisfaction client avec les cabinets alimentés par l'IA",
        },
      },
    },

    // Security Banner
    security: {
      title: "Sécurité de Niveau Entreprise",
      description:
        "Vos données clients sont protégées avec des mesures de sécurité de niveau bancaire.",
      features: [
        "Chiffrement de bout en bout",
        "Conforme RGPD",
        "Certifié SOC 2",
        "Audits de sécurité réguliers",
      ],
      learnMore: "En savoir plus sur notre sécurité",
    },

    // Cookie Notice
    cookies: {
      message:
        "Nous utilisons des cookies pour améliorer votre expérience et analyser l'utilisation du site.",
      accept: "Accepter",
      decline: "Refuser",
      learnMore: "En savoir plus",
    },

    // FAQ
    faq: {
      title: "Questions Fréquemment Posées",
      questions: {
        q1: {
          question: "Comment AiLex protège-t-il mes données ?",
          answer:
            "AiLex utilise une sécurité de niveau entreprise avec chiffrement de bout en bout, conformité RGPD et audits de sécurité réguliers pour protéger vos données clients.",
        },
        q2: {
          question: "AiLex peut-il gérer les documents juridiques belges ?",
          answer:
            "Oui, AiLex est spécifiquement formé sur le droit belge et peut gérer des documents en français, néerlandais et anglais selon les standards juridiques belges.",
        },
        q3: {
          question: "Combien de temps puis-je économiser avec AiLex ?",
          answer:
            "La plupart des avocats économisent 8-12 heures par semaine sur les tâches administratives, la recherche et la rédaction de documents, permettant plus de temps pour le travail facturable.",
        },
        q4: {
          question: "Y a-t-il un essai gratuit ?",
          answer:
            "Oui, nous offrons un essai gratuit de 14 jours avec accès complet à toutes les fonctionnalités. Aucune carte de crédit requise.",
        },
        q5: {
          question: "Puis-je annuler à tout moment ?",
          answer:
            "Absolument. Vous pouvez annuler votre abonnement à tout moment sans frais d'annulation ou engagements à long terme.",
        },
      },
    },

    // Blog
    blog: {
      title: "Insights IA Juridique",
      subtitle:
        "Restez informé des dernières nouveautés en technologie juridique et IA",
      readMore: "Lire la suite",
      backToBlog: "Retour au blog",
      relatedPosts: "Articles connexes",
      categories: "Catégories",
      tags: "Étiquettes",
      publishedOn: "Publié le",
      author: "Auteur",
    },
  },

  nl: {
    // Page metadata
    pageTitle: "AiLex - AI Juridische Assistent voor Belgische Advocaten",
    metaDescription:
      "AI-aangedreven juridische assistent gebouwd voor Belgisch recht. Onderzoek, stel op en organiseer zoals een groot kantoor tegen tarieven voor individuele praktijken.",

    // Hero section
    heroTitle:
      "Uw AI Juridische Assistent — Gebouwd voor Belgisch Recht. Werkt Terwijl U in de Rechtbank Bent.",
    heroSubtitle:
      "Onderzoek als een juridische documentalist, schrijf als een jurist, organiseer als een assistent — voor de prijs van een eenmanskantoor.",
    heroCallToAction: "Probeer AiLex Gratis",
    heroSecondaryAction: "Bekijk demo",
    heroTagline: "AiLex doet het ALLEMAAL voor u.",
    heroBadges: {
      startImmediately: "Begin meteen",
      noSetupNeeded: "Geen installatie nodig",
      noLearningCurve: "Geen leercurve",
      aiReceptionist: "AI-receptionist",
      jurisdictionResearch: "Gericht onderzoek — Belgisch recht",
    },

    dashboard: {
      title: "AiLex Dashboard",
      firmLabel: "Kantoor: Van den Berg & Partners",
      readyStatus: "Klaar",
      activeMatters: "Actieve Zaken",
      weeklyIncrease: "+2 deze week",
      recentActivity: "Recente Activiteit",
      documentAnalysis: "Documentanalyse",
      employmentContract: "Arbeidscontract – Ontwerp.pdf",
      clientIntake: "Cliëntopname",
      tenantDispute: "Daniel Delvaux (Huurgeschil)",
      upcoming: "BINNENKORT",
      seeAll: "Alles bekijken",
      filingDeadline: "Indiendatum",
      delvauxCase: "15 mei – Delvaux tegen Verhuurder",
      courtHearing: "Rechtszitting",
      justiceOfPeace: "20 mei – Vredegerecht, Brussel",
      schedule: "Plannen",
      openDashboard: "Dashboard openen",
    },

    // Navigation
    navFeatures: "Product",
    navPricing: "Prijzen",
    navBlog: "Blog",
    navFaq: "FAQ",
    navContact: "Contact",
    navLogin: "Inloggen",
    navStartTrial: "Probeer AiLex Gratis",

    // Social Proof - Practice Areas
    practiceAreas: {
      personalInjury: "Letselschade",
      family: "Familierecht",
      criminal: "Strafrecht",
      civilLitigation: "Civiele Procedures",
      employment: "Arbeidsrecht",
      immigration: "Immigratierecht",
      businessCorporate: "Ondernemingsrecht",
      intellectualProperty: "Intellectueel Eigendom",
    },

    // Problem Solution Section
    problemTitle: "Het Probleem van Individuele Praktijken",
    problemSubtitle: "Kleine kantoren staan voor grote uitdagingen",
    problems: {
      unbilledTime: {
        title: "40% advocaattijd is niet-factureerbare admin.",
        description:
          "De gemiddelde solo-advocaat verliest bijna de helft van hun potentiële factureerbare uren aan administratieve taken.",
      },
      missedCalls: {
        title: "Gemiste intake-oproepen kosten solo's €7k+/jaar.",
        description:
          "Elke gemiste oproep is een potentiële cliënt verloren. Kleine kantoren zonder receptiepersoneel verliezen jaarlijks duizenden euros.",
      },
      expensiveSoftware: {
        title: "Grote kantoor software begint bij €199/gebruiker.",
        description:
          "Enterprise juridische oplossingen zijn geprijsd voor grote kantoren, waardoor ze onbetaalbaar zijn voor solo-praktijken.",
      },
    },

    solutionTitle:
      "Stel Nooit Meer een Verzoekschrift Op — AiLex Schrijft Het voor U",
    solutionSubtitle: "8-12 uur bespaard/week",
    solutionDescription:
      "AiLex vindt relevante zaken, brengt belangrijke statuten naar voren en stelt memo's of verzoekschriften op — zoals een junior advocaat, minus de overhead.",
    solutionBadge: "8-12 uur bespaard/week",

    bigLawTechTitle: "Big-Law Tech, prijzen voor kleine kantoren",
    bigLawTechDescription:
      "AiLex levert AI-tools waarvoor je normaal €199/gebruiker betaalt — vanaf slechts €99/maand voor 2 gebruikers.",
    testimonialQuote: "AiLex heeft ons dit jaar meer dan €5.000 bespaard.",
    costSavings: "75% kostenbesparing",
    enterpriseLegalTech: "Enterprise Legal Tech",
    ailexTeamPlan: "AiLex Teamplan",
    oneUser: "1 Gebruiker",
    twoUsers: "2 Gebruikers",
    price199: "€199",
    price99: "€99",

    // Solution section
    solutionSectionTitle: "Met AiLex Speel je in de Hoogste Klasse",
    solutionSectionSubtitle: "Oplossing",
    solutionSectionDescription:
      "AiLex is je fulltime assistent — hij neemt oproepen aan, noteert details, zoekt wetten op en stelt documenten op… zodat jij weer kunt",
    solutionSectionHighlight: "LEVEN.",
    solutionSectionBenefits:
      "Meer factureerbaar werk. Meer cliënten. Minder admin. Meer successen in de rechtszaal.",

    solutionSteps: {
      step1: "Stap 1: Oproep vastleggen",
      step2: "Stap 2: Dossier organiseren",
      step3: "Stap 3: Werk uitwerken",
    },

    solutionHeadlines: {
      headline1: "Mis nooit een lead — AiLex beantwoordt elke oproep",
      headline2: "Mis nooit een deadline — AiLex houdt elk dossier op koers",
      headline3:
        "Herschrijf nooit meer een juridisch document — AiLex schrijft het voor je",
    },

    solutionTimeSavings: {
      badge1: "3-5 uur bespaard/week",
      badge2: "5-7 uur bespaard/week",
      badge3: "8-12 uur bespaard/week",
    },

    solutionDescriptions: {
      description1:
        "De AI-receptionist van AiLex beantwoordt binnenkomende oproepen meteen, noteert de naam van de cliënt, het type zaak en de urgentie, en slaat alles op in je dashboard.",
      description2:
        "AiLex organiseert automatisch je documenten en deadlines, markeert urgente zaken en stelt volgende stappen voor — zodat je altijd weet wat het belangrijkst is.",
      description3:
        "AiLex zoekt rechtspraak, markeert kernwetten en schrijft je juridische documenten — zoals een jurist, zonder het prijskaartje.",
    },

    solutionClientQuote:
      "Hallo, met Daniel. Ik zoek hulp bij een huurgeschil. Ik vond uw kantoor online en wilde weten of u huurgeschillen behandelt.",

    solutionTestimonialQuote:
      "Alles wat ik nodig had was al gelogd toen ik de app opende.",

    solutionCaseName: "Daniel t. Verhuurder",

    aiPrioritized: "AI-Geprioriteerd",

    noticeOfDisputeDraft: "Kennisgeving van Geschil Concept",

    dueToday: "Vandaag Inleveren",

    medicalCertificate: "Medisch Certificaat.pdf",

    setCourtDate: "Rechtbankdatum instellen",

    highPriority: "Hoge Prioriteit",

    responseLetter: "Antwoordbrief",

    aiSuggested: "AI-voorstel",

    uploaded: "Geüpload",

    organizationTestimonial:
      "Ik hoef niet meer achter documenten aan te jagen. Alles staat klaar als ik inlog.",

    legalResearchLabels: {
      cited17x: "17x geciteerd",
      belgianLaw: "Belgisch Recht",
      draftClientMemo: "Concept: Cliëntnota — Antwoord op de vordering",
      civilCodeArt: "Burgerlijk Wetboek Art. 1728",
      relevant: "Relevant",
    },

    researchTestimonial:
      "Het is alsof ik mijn eigen juridisch onderzoeker heb.",

    typewriterPhrases: {
      phrase1: "AiLex beantwoordt uw oproepen",
      phrase2: "AiLex beantwoordt uw oproepen, organiseert deadlines",
      phrase3:
        "AiLex beantwoordt uw oproepen, organiseert deadlines, stelt documenten op",
      phrase4:
        "AiLex beantwoordt uw oproepen, organiseert deadlines, stelt documenten op en vindt relevante jurisprudentie",
    },
    typewriterSubtitle:
      "AiLex doet onderzoek als een juridische documentalist, schrijft als een jurist en organiseert als een assistent — allemaal aan het tarief van een eenmanskantoor.",

    solutionDemoLabels: {
      newClientCall: "Nieuw Cliëntgesprek",
      callHandledByAiLex: "Oproep Afgehandeld door AiLex",
      capturedAutomatically: "📡 Automatisch vastgelegd door AI",
      voiceRecording: "Spraakopname",
      clientName: "Cliëntnaam:",
      caseType: "Zaaktype:",
      urgency: "Urgentie:",
      syncedToAiLex: "✅ Gesynchroniseerd met AiLex",
    },

    // Features
    featuresTitle: "Alles wat U Nodig Heeft om Uw Praktijk te Runnen",
    featuresSubtitle:
      "Van intake tot oplossing, AiLex behandelt het werk dat u ervan weerhoudt recht te beoefenen.",
    featuresReady: "Klaar",
    featuresSeeAction: "Bekijk in actie",
    featuresTrainingRequired: "Training vereist: 0 uur. 0 minuten. 0 seconden.",
    featuresEasyBreezyReady: "Eenvoudig. Makkelijk. Klaar voor gebruik.",
    featuresCoreFeatures: "Belangrijkste Functies",
    featuresMainTitle: "Wat AiLex Doet voor Solo-Advocaten & Kleine Kantoren",

    features: {
      research: {
        title: "Onderzoeksmodule",
        description:
          "AiLex uploadt uw zaken, juridische teksten of pleidooien — vindt dan referenties, maakt samenvattingen en extraheert belangrijke argumenten in seconden.",
        demoTitle: "Onderzoek Demo",
        demoContent: {
          title: "Resultaten van juridisch onderzoek",
          personalInjuryLabel:
            "Toepasselijke bepaling (burgerlijke aansprakelijkheid):",
          personalInjuryText:
            "Belgisch Burgerlijk Wetboek, artikel 1382 – Algemene aansprakelijkheid voor schade veroorzaakt door fout",
          keyPrecedentLabel: "Belangrijk precedent:",
          keyPrecedentText:
            "Hof van Cassatie (2019) – Gedeelde schuld toegepast bij verkeersongeval",
          relatedCasesLabel: "Vergelijkbare zaken:",
          relatedCasesText:
            "18 soortgelijke zaken gevonden bij Belgische rechtbanken in de afgelopen 5 jaar",
        },
      },
      drafting: {
        title: "Schrijfco-piloot",
        description:
          "AiLex stelt procedurestukken, pleidooien en correspondentie op — in de juiste juridische taal en opmaak voor uw rechtsgebied.",
        demoTitle: "Opstel Demo",
        demoContent: {
          title: "Verzoek tot overlegging van stukken",
          petitionText:
            "Eiser, Thomas De Smet, bijgestaan door zijn raadsman, verzoekt de rechtbank om de tegenpartij te gelasten de relevante documenten over te leggen, overeenkomstig artikel 877 van het Gerechtelijk Wetboek.",
          aiSuggestionLabel: "AI-voorstel:",
          aiSuggestionText:
            "Voeg een verwijzing toe naar artikel 877 van het Gerechtelijk Wetboek – Verzoek tot overlegging van stukken",
        },
      },
      receptionist: {
        title: "AI-Receptionist",
        description:
          "Met AiLex mist u nooit meer een telefoontje van een cliënt. Onze AI-Receptionist beantwoordt oproepen, kwalificeert leads en plant afspraken in — 24/7.",
        demoTitle: "Spraakagent Demo",
        sampleConversationLabel: "Voorbeeld van een intakegesprek:",
        sampleConversation: {
          client: "Ik heb hulp nodig bij een verkeersovertreding.",
          ailex:
            "Begrijpelijk, u zoekt dus juridische bijstand voor een verkeersinbreuk. Kunt u me zeggen wanneer het incident heeft plaatsgevonden?",
        },
      },
      caseOrganizer: {
        title: "Dossierbeheerder",
        description:
          "AiLex houdt uw dossiers georganiseerd — met uw bestanden, notities, deadlines en cliëntcommunicatie gebundeld in één beveiligde werkomgeving.",
        demoTitle: "Zaakbeheer Demo",
        demoContent: {
          caseTitle: "De Smet t. Dupont - Persoonlijk letsel",
          filesLabel: "Bestanden (12)",
          filesDescription:
            "Medische certificaten, ongevalfoto's, getuigenverklaringen",
          nextDeadlineLabel: "Volgende deadline",
          nextDeadlineDescription:
            "Door rechtbank bevolen documentuitwisseling vervalt over 5 dagen",
          latestUpdateLabel: "Laatste update:",
          latestUpdateDescription:
            "Cliënt gebeld — herstelt goed, klaar voor zitting",
        },
      },
      workflow: {
        title: "Taak & Workflow Automatisering",
        description:
          "Van intake tot oplossing houdt AiLex u op koers — wijs taken toe, volg de voortgang en automatiseer het administratieve werk dat u vroeger handmatig deed.",
        demoTitle: "Workflow Demo",
        demoContent: {
          title: "Nieuwe cliënt workflow",
          sendWelcomeEmail: "Welkomst-e-mail versturen",
          createClientFile: "Cliëntdossier aanmaken",
          scheduleFirstMeeting: "Eerste afspraak inplannen",
          collectSupportingDocuments: "Ondersteunende documenten verzamelen",
          automaticReminder: "Automatische herinnering naar cliënt verzonden ✓",
        },
      },
      deadlines: {
        title: "Deadlines & Herinneringen",
        description:
          "AiLex houdt uw deadlines bij, stuurt herinneringen en synchroniseert met uw agenda — voor een vlotte en stressvrije planning.",
        demoTitle: "Agenda Integratie Demo",
        demoContent: {
          title: "Aankomende deadlines",
          responseToOpposingBrief: "Reactie op dupliek",
          today: "Vandaag",
          courtFilingDeadline: "Rechtbank indieningsdeadline",
          threeDays: "3 dagen",
          clientMeeting: "Cliëntafspraak",
          oneWeek: "1 week",
          syncedWithCalendar: "📅 Gesynchroniseerd met Google Agenda",
        },
      },
    },

    // Pricing
    pricingTitle: "Eenvoudige, Transparante Prijzen",
    pricingSubtitle: "Kies het plan dat bij uw praktijk past",
    billingMonthly: "Maandelijks",
    billingAnnual: "Jaarlijks",
    annualDiscount: "Bespaar 15%",

    pricingPlans: {
      solo: {
        name: "Solo",
        subtitle: "ADVOCAAT & ASSISTENT",
        features: [
          "Advocaat + 1 Gratis Assistent zetel",
          "Kies 1 praktijkgebied (bijv. Civiel, Familie, Arbeidsrecht)",
          "50 onderzoeksvragen & 20 documentopstellen/maand",
          "Cliënt intake (Email + formulier)",
          "Taak & deadline beheer",
          "Zaakbeheer & Document upload",
          "➕ Voeg AI Receptionist toe voor intake & planning (100 min/maand) voor €29/maand",
          "➕ Voeg extra gebruiker toe voor €29/maand",
        ],
        cta: "Start gratis proefperiode",
      },
      team: {
        name: "Team",
        subtitle: "KLEIN KANTOOR",
        features: [
          "5 gebruikers inbegrepen",
          "✅ Alles in SOLO, plus:",
          "Bevat meerdere kern Belgische praktijkgebieden",
          "Onbeperkt onderzoek & opstellen*",
          "AI Receptionist voor intake & planning (250 mins/maand)",
          "➕ Voeg extra gebruiker toe voor €29/maand",
        ],
        cta: "Start gratis proefperiode",
      },
      scale: {
        name: "Schaal",
        subtitle: "GROEIENDE BEDRIJVEN",
        features: [
          "10 gebruikers inbegrepen",
          "✅ Alles in TEAM, plus:",
          "AI Receptionist (500 mins/maand)",
          "Aangepaste API integratie",
          "Toegewijde onboarding & ondersteuning",
          "Prioritaire toegang tot nieuwe jurisdicties",
          "➕ Voeg extra gebruiker toe voor €29/maand",
        ],
        cta: "Contact verkoop",
      },
    },

    // Footer
    footerTagline:
      "Gebouwd voor kleine kantoren, aangedreven door grote technologie.",
    footerProduct: "Product",
    footerResources: "Bronnen",
    footerNewsletter: "Ontvang Wekelijkse AI Tips",
    footerNewsletterDescription:
      "Sluit u aan bij 2.000+ advocaten die wekelijkse inzichten ontvangen over AI-tools, praktijkbeheer en juridische tech trends.",
    footerEmailPlaceholder: "Voer uw email in",
    footerSubscribe: "Abonneren",
    footerCopyright: "© 2024 AiLex. Alle rechten voorbehouden.",

    // Common UI
    learnMore: "Meer informatie",
    getStarted: "Aan de slag",
    contactSales: "Contact verkoop",
    startFreeTrial: "Start gratis proefperiode",
    viewDemo: "Bekijk demo",
    close: "Sluiten",

    // Legal terminology
    legalTerms: {
      lawyer: "Advocaat",
      attorney: "Advocaat",
      jurist: "Jurist",
      legalAssistant: "Juridische Assistent",
      aiLegalAssistant: "AI Juridische Assistent",
      legalResearch: "Juridisch Onderzoek",
      aiReceptionist: "AI Receptionist",
      caseManagement: "Zaakbeheer",
      documentDrafting: "Document Opstellen",
      clientIntake: "Cliënt Intake",
    },

    // ROI Calculator
    roiCalculator: {
      badge: "ROI-calculator",
      title: "Bereken Uw ROI met AiLex",
      subtitle: "Zie hoeveel tijd en geld u zou kunnen besparen",
      mainTitle: "Uw besparing in één klik.",
      mainSubtitle:
        "Bereken hoeveel uw kantoor elke maand uitspaart met AiLex — door repetitieve juridische taken te automatiseren en vaste kosten te verlagen.",
      estimateYourSavings: "Schat Uw Besparingen In",
      useSlidersInstruction:
        "Gebruik de schuifregelaars hieronder om de bespaarde tijd aan te passen",
      lawyerTime: "Advocaat Tijd",
      lawyerTimeSubtitle:
        "Hoeveel uren per week besteedt u aan administratie in plaats van factureerbaar werk?",
      juristTime: "Jurist (Juridisch Adviseur) Tijd",
      juristTimeSubtitle: "Hoeveel uren per week kan AiLex besparen?",
      legalAdminTime: "Juridisch Administratief Assistent Tijd",
      legalAdminTimeSubtitle: "Hoeveel uren per week kan AiLex besparen?",
      monthlySavingsSummary: "Uw Maandelijkse Besparingen Samenvatting",
      monthlyTimeSavings: "Maandelijkse Tijdbesparingen",
      estimatedValue: "Geschatte Waarde",
      perMonth: "per maand",
      hoursEveryWeek: "uren elke week",
      roleByRoleBreakdown: "Rol-voor-Rol Uitsplitsing",
      lawyer: "Advocaat",
      jurist: "Jurist (Juridisch Adviseur)",
      legalAdminAssistant: "Juridisch Administratief Assistent",
      basedOnAverageRates:
        "Gebaseerd op gemiddelde nationale uurtarieven per rol",
      emailSavingsReport: "E-mail mij dit besparingsrapport",
      startSavingCta: "Begin met besparen met AiLex — Probeer 14 dagen gratis",
      noCreditCardRequired:
        "Geen creditcard vereist. Volledige toegang tot alle functies.",
      getStarted: "Aan de Slag",
      seeOurPricing: "Bekijk onze prijzen",
      currentSituation: "Huidige Situatie",
      withAilex: "Met AiLex",
      savings: "Uw Besparingen",
      hourlyRate: "Uw uurtarief (€)",
      hoursPerWeek: "Admin uren per week",
      calculate: "Bereken Besparingen",
      results: {
        timeSaved: "Tijd Bespaard",
        moneySaved: "Geld Bespaard",
        efficiency: "Efficiëntiewinst",
        perMonth: "per maand",
        perYear: "per jaar",
      },
    },

    // Testimonials
    testimonials: {
      title: "Vertrouwd door Juridische Professionals",
      subtitle: "Zie wat advocaten zeggen over AiLex",
      quotes: {
        quote1:
          "AiLex heeft getransformeerd hoe we zaakonderzoek behandelen. Wat uren kostte, duurt nu minuten.",
        author1: "Marie Dubois",
        title1: "Familierecht Advocaat, Brussel",
        quote2:
          "De AI receptionist mist nooit een oproep. Onze cliënt intake is met 40% gestegen.",
        author2: "Jan Vermeulen",
        title2: "Letselschade Advocaat, Antwerpen",
        quote3:
          "Eindelijk juridische tech die echt Belgisch recht begrijpt. Game changer voor onze praktijk.",
        author3: "Sophie Laurent",
        title3: "Civiele Procedures Advocaat, Gent",
      },
    },

    // Industry Insights
    industryInsights: {
      title: "De Toekomst van Juridische Praktijk",
      subtitle: "AI transformeert hoe advocaten werken",
      stats: {
        stat1: {
          number: "73%",
          label:
            "van advocaten rapporteert dat AI hen 5+ uur per week bespaart",
        },
        stat2: {
          number: "€12.000",
          label:
            "gemiddelde jaarlijkse besparingen per advocaat die AI-tools gebruikt",
        },
        stat3: {
          number: "2,5x",
          label: "sneller document opstellen met AI-assistentie",
        },
        stat4: {
          number: "95%",
          label: "cliënttevredenheid bij AI-aangedreven kantoren",
        },
      },
    },

    // Security Banner
    security: {
      title: "Enterprise-Grade Beveiliging",
      description:
        "Uw cliëntgegevens zijn beschermd met beveiligingsmaatregelen op bankniveau.",
      features: [
        "End-to-end encryptie",
        "AVG-compliant",
        "SOC 2 gecertificeerd",
        "Regelmatige beveiligingsaudits",
      ],
      learnMore: "Meer informatie over onze beveiliging",
    },

    // Cookie Notice
    cookies: {
      message:
        "We gebruiken cookies om uw ervaring te verbeteren en sitegebruik te analyseren.",
      accept: "Accepteren",
      decline: "Weigeren",
      learnMore: "Meer informatie",
    },

    // FAQ
    faq: {
      title: "Veelgestelde Vragen",
      questions: {
        q1: {
          question: "Hoe houdt AiLex mijn gegevens veilig?",
          answer:
            "AiLex gebruikt enterprise-grade beveiliging met end-to-end encryptie, AVG-compliance en regelmatige beveiligingsaudits om uw cliëntgegevens te beschermen.",
        },
        q2: {
          question: "Kan AiLex Belgische juridische documenten behandelen?",
          answer:
            "Ja, AiLex is specifiek getraind op Belgisch recht en kan documenten in het Frans, Nederlands en Engels behandelen volgens Belgische juridische standaarden.",
        },
        q3: {
          question: "Hoeveel tijd kan ik besparen met AiLex?",
          answer:
            "De meeste advocaten besparen 8-12 uur per week op administratieve taken, onderzoek en document opstellen, waardoor meer tijd overblijft voor factureerbaar werk.",
        },
        q4: {
          question: "Is er een gratis proefperiode?",
          answer:
            "Ja, we bieden een 14-daagse gratis proefperiode met volledige toegang tot alle functies. Geen creditcard vereist.",
        },
        q5: {
          question: "Kan ik op elk moment opzeggen?",
          answer:
            "Absoluut. U kunt uw abonnement op elk moment opzeggen zonder opzegkosten of langetermijnverplichtingen.",
        },
      },
    },

    // Blog
    blog: {
      title: "Juridische AI Inzichten",
      subtitle:
        "Blijf op de hoogte van het laatste in juridische technologie en AI",
      readMore: "Lees meer",
      backToBlog: "Terug naar blog",
      relatedPosts: "Gerelateerde berichten",
      categories: "Categorieën",
      tags: "Tags",
      publishedOn: "Gepubliceerd op",
      author: "Auteur",
    },
  },
};

export function getTranslations(language: Language): Translations {
  return translations[language] || translations.en;
}

export function getCurrentLanguage(): Language {
  if (typeof window !== "undefined") {
    const path = window.location.pathname;
    if (path.startsWith("/be-fr")) return "fr";
    if (path.startsWith("/be-nl")) return "nl";
    if (path.startsWith("/be")) return "en";
  }
  return "en";
}

export function useTranslations(): Translations {
  const currentLanguage = getCurrentLanguage();
  return getTranslations(currentLanguage);
}
