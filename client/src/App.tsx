import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

import Home from "@/pages/Home";
import StatePage from "@/pages/StatePage";
import Blog from "@/pages/Blog";
import BlogPost from "@/pages/BlogPost";
import BelgiumPage from "@/pages/BelgiumPage";
import BelgiumBlog from "@/pages/BelgiumBlog";
import BelgiumBlogPost from "@/pages/BelgiumBlogPost";
import BelgiumPageFR from "@/pages/be-fr/BelgiumPage";
import BelgiumBlogFR from "@/pages/be-fr/BelgiumBlog";
import BelgiumBlogPostFR from "@/pages/be-fr/BelgiumBlogPost";
import BelgiumPageNL from "@/pages/be-nl/BelgiumPage";
import BelgiumBlogNL from "@/pages/be-nl/BelgiumBlog";
import BelgiumBlogPostNL from "@/pages/be-nl/BelgiumBlogPost";
import Login from "@/pages/Login";
import SecurityWhitepaper from "@/pages/SecurityWhitepaper";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/tx" component={() => <StatePage state="TX" />} />
      <Route path="/fl" component={() => <StatePage state="FL" />} />
      <Route path="/ny" component={() => <StatePage state="NY" />} />

      {/* Belgian routes - English */}
      <Route path="/be" component={BelgiumPage} />
      <Route path="/be/blog" component={BelgiumBlog} />
      <Route path="/be/blog/:slug" component={BelgiumBlogPost} />

      {/* Belgian routes - French */}
      <Route path="/be-fr" component={BelgiumPageFR} />
      <Route path="/be-fr/blog" component={BelgiumBlogFR} />
      <Route path="/be-fr/blog/:slug" component={BelgiumBlogPostFR} />

      {/* Belgian routes - Dutch */}
      <Route path="/be-nl" component={BelgiumPageNL} />
      <Route path="/be-nl/blog" component={BelgiumBlogNL} />
      <Route path="/be-nl/blog/:slug" component={BelgiumBlogPostNL} />

      <Route path="/blog" component={Blog} />
      <Route path="/blog/:slug" component={BlogPost} />
      <Route path="/login" component={Login} />
      <Route path="/security-whitepaper" component={SecurityWhitepaper} />
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
